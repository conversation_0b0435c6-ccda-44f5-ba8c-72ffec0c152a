import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';
import { createPMORecordFromForm } from '../../../lib/firebase/pmoCollection';
import { PMOAssessmentAgent } from '../../../lib/agents/pmo/PMOAssessmentAgent';
import { PMOFormInput, AgenticTeamId } from '../../../lib/agents/pmo/PMOInterfaces';
import { QueryDocumentsAgent } from '../../../components/Agents/QueryDocumentsAgent';
import { StrategicDirectorAgent } from 'lib/agents/marketing/StrategicDirectorAgent';
import { createLlmService } from '../../../lib/tools/llmServiceAdapter';

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await req.json();
    const {
      projectName,
      description,
      selectedProvider, // Used to create the LLM service
      selectedModel,    // Used to specify the model for the LLM service
      customContext,
      selectedDocumentId,
      selectedCategory,
      generatedPmoAssessment // This is the requirements specification generated in the previous step
    } = body;

    // Validate required fields
    if (!projectName || !description) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Create an LLM service based on the selected provider (or default to openai)
    const llmService = createLlmService(selectedProvider || 'openai');

    // Create the PMO assessment agent with the LLM service
    const pmoAgent = new PMOAssessmentAgent({
      userId: session.user.email,
      includeExplanation: false,
      streamResponse: false
    }, llmService);

    // Create and set the query documents agent
    // Corrected: Constructor expects a string (userId), not an object.
    const queryDocumentsAgent = new QueryDocumentsAgent({
      userId: session.user.email
    });
    // If 'includeExplanation: false' was intended for this agent,
    // it would need to be set via a property or method if available, e.g.:
    // queryDocumentsAgent.includeExplanation = false;
    // For this fix, we only address the constructor error.
    pmoAgent.setQueryDocumentsAgent(queryDocumentsAgent);

    // Create and set the strategic director agent
    // Corrected: Constructor expects a string (userId), not an object.
    const strategicDirectorAgent = new StrategicDirectorAgent(session.user.email);
    // If 'includeExplanation: false' was intended for this agent,
    // it would need to be set via a property or method if available, e.g.:
    // strategicDirectorAgent.includeExplanation = false;
    // For this fix, we only address the constructor error.
    pmoAgent.setStrategicDirectorAgent(strategicDirectorAgent);

    // Create the form input
    const formInput: PMOFormInput = {
      title: projectName,
      description,
      priority: 'Medium', // Hardcoded priority
      category: 'Unknown', // Hardcoded category
      modelName: selectedModel, // Pass the selected model to the LLM service
      modelProvider: selectedProvider, // Pass the selected provider to the LLM service
      contextOptions: {
        customContext: customContext || null,
        fileIds: selectedDocumentId ? [selectedDocumentId] : null,
        categoryIds: selectedCategory ? [selectedCategory] : null
      }
    };

    // We'll use the pre-generated requirements specification if available
    let pmoAssessment = '';
    let selectedTeams: AgenticTeamId[] = [];
    let requirementsDocumentId: string | undefined;

    if (generatedPmoAssessment) {
      // Use the pre-generated requirements specification
      pmoAssessment = generatedPmoAssessment;

      // Simple keyword-based team selection (same as in pmo-assessment endpoint)
      const descriptionLower = description.toLowerCase();
      if (descriptionLower.includes('market') || descriptionLower.includes('brand') || descriptionLower.includes('campaign')) {
        selectedTeams.push(AgenticTeamId.Marketing);
      } else if (descriptionLower.includes('research') || descriptionLower.includes('analysis') || descriptionLower.includes('data')) {
        selectedTeams.push(AgenticTeamId.Research);
      } else if (descriptionLower.includes('software') || descriptionLower.includes('develop') || descriptionLower.includes('code') || descriptionLower.includes('app')) {
        selectedTeams.push(AgenticTeamId.SoftwareDesign);
      } else if (descriptionLower.includes('sales') || descriptionLower.includes('client') || descriptionLower.includes('revenue')) {
        selectedTeams.push(AgenticTeamId.Sales);
      } else if (descriptionLower.includes('business') || descriptionLower.includes('process') || descriptionLower.includes('strategy')) {
        selectedTeams.push(AgenticTeamId.BusinessAnalysis);
      } else {
        // Default to Research if no keywords match
        selectedTeams.push(AgenticTeamId.Research);
      }

      // Generate PDF and save agent output
      if (pmoAgent.hasPDFGenerator()) {
        try {
          const pdfResult = await pmoAgent.generatePDF(projectName, pmoAssessment);
          if (pdfResult.success && pdfResult.fileUrl) {
            const outputResult = await pmoAgent.saveAgentOutput(
              projectName,
              pmoAssessment,
              pdfResult.fileUrl,
              selectedTeams,
              'Medium'
            );
            requirementsDocumentId = outputResult.id;
          }
        } catch (error) {
          console.error('Error generating PDF or saving agent output:', error);
        }
      }
    } else {
      // If no pre-generated requirements specification, generate it now
      const assessmentResult = await pmoAgent.generateAssessment(formInput);
      pmoAssessment = assessmentResult.pmoAssessment;
      selectedTeams = assessmentResult.selectedTeams;
      requirementsDocumentId = assessmentResult.requirementsDocumentId;
    }

    // Prepare data for PMO record creation
    const formData = {
      title: projectName,
      description,
      priority: 'Medium' as const,
      category: 'Unknown',
      sourceFile: selectedDocumentId || null, // Use null instead of undefined
      fileName: selectedCategory || `${projectName} - Requirements Specification`, // Ensure fileName is always a string
      customContext: customContext || null, // Use null instead of undefined
      selectedFileId: selectedDocumentId || null, // Use null instead of undefined
      selectedCategory: selectedCategory || null, // Use null instead of undefined
      pmoAssessment: pmoAssessment
    };

    // Create the PMO record in Firebase
    const pmoId = await createPMORecordFromForm(session.user.email, formData);

    // Return the result
    return NextResponse.json({
      success: true,
      pmoId,
      pmoAssessment: pmoAssessment,
      selectedTeams: selectedTeams,
      requirementsDocumentId: requirementsDocumentId
    });
  } catch (error: any) {
    console.error('Error submitting PMO request:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to submit PMO request' },
      { status: 500 }
    );
  }
}
