import { NextRequest, NextResponse } from 'next/server';
import { addDoc, collection, updateDoc, doc } from 'firebase/firestore';
import { db } from '../../../components/firebase';
import { addAgentOutput } from '../../../lib/firebase/agentOutputs';
import { processPMODocument } from '../../../lib/pmo/processPMODocument';

/**
 * API endpoint for teams to create strategic implementation plans
 * This replaces the PMO-generated strategic plans with team-specific plans
 */
export async function POST(request: NextRequest) {
  try {
    const {
      pmoId,
      teamId,
      teamName,
      projectTitle,
      strategicPlanContent,
      userId,
      category,
      agentType,
      metadata
    } = await request.json();

    // Validate required fields
    if (!pmoId || !teamId || !teamName || !projectTitle || !strategicPlanContent || !userId) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const currentDate = new Date().toISOString();
    const documentTitle = `Strategic Implementation Plan - ${projectTitle} (${teamName}) - (${pmoId})`;

    // Save to Agent_Output collection with proper PMO integration
    const agentOutputData = {
      userId,
      agentType: agentType || `${teamName}_Strategic_Director`,
      title: documentTitle,
      content: strategicPlanContent,
      createdAt: new Date(),
      metadata: {
        ...metadata,
        pmoId,
        teamId,
        teamName,
        projectTitle,
        category: category || `PMO -  ${projectTitle} - ${pmoId}`,
        documentType: 'Strategic Implementation Plan',
        generatedAt: currentDate,
        source: 'Team',
        createdBy: teamName,
        strategicPlanVersion: '1.0'
      }
    };

    // Add to Agent_Output collection
    const agentOutputResult = await addAgentOutput(agentOutputData);
    
    if (!agentOutputResult.success) {
      throw new Error(agentOutputResult.error || 'Failed to save agent output');
    }

    // Process as PMO document for PDF generation and bytestore storage
    const pmoDocumentResult = await processPMODocument({
      title: documentTitle,
      content: strategicPlanContent,
      pmoId,
      userId,
      category: category || `PMO -  ${projectTitle} - ${pmoId}`,
      metadata: {
        ...agentOutputData.metadata,
        agentOutputId: agentOutputResult.id,
        pdfGenerated: true
      }
    });

    if (!pmoDocumentResult.success) {
      console.warn('Failed to process PMO document, but agent output was saved:', pmoDocumentResult.error);
    }

    // Update team notification status if it exists
    try {
      // This would require finding and updating the team notification
      // For now, we'll create a completion record
      await addDoc(
        collection(db, 'users', userId, 'teamCompletions'),
        {
          pmoId,
          teamId,
          teamName,
          projectTitle,
          strategicPlanCreated: true,
          strategicPlanId: agentOutputResult.id,
          completedAt: new Date(),
          documentTitle,
          category: category || `PMO -  ${projectTitle} - ${pmoId}`,
          metadata: {
            agentOutputId: agentOutputResult.id,
            pdfUrl: pmoDocumentResult.fileUrl
          }
        }
      );
    } catch (updateError) {
      console.warn('Failed to update team notification status:', updateError);
    }

    return NextResponse.json({
      success: true,
      agentOutputId: agentOutputResult.id,
      documentTitle,
      pdfUrl: pmoDocumentResult.fileUrl,
      message: `Strategic implementation plan created successfully by ${teamName} team`
    });

  } catch (error) {
    console.error('Error creating team strategic plan:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to create strategic plan' 
      },
      { status: 500 }
    );
  }
}
