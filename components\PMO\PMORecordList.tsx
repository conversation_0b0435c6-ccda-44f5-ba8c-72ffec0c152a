'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Clock, Calendar, Users, ChevronRight, AlertCircle, CheckCircle, XCircle, PauseCircle, Eye, Trash2, FileText, Send, Loader2 } from 'lucide-react';
import { PMORecord, PMORecordStatus, PMORecordPriority, AgenticTeamId } from '../../lib/agents/pmo/PMOInterfaces';
import { deletePMORecord } from '../../lib/firebase/pmoCollection';
import { useAuth } from '../../app/context/AuthContext';
import MarkdownRenderer from '../MarkdownRenderer'; // Ensure this path is correct
import { toast } from '../../components/ui/use-toast';

interface PMORecordListProps {
  records: PMORecord[];
  onRefresh: () => void;
}

const PMORecordList: React.FC<PMORecordListProps> = ({ records, onRefresh }) => {
  const router = useRouter();
  const { user } = useAuth();
  const [expandedRecordId, setExpandedRecordId] = useState<string | null>(null);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const [isCreatingRequirement, setIsCreatingRequirement] = useState(false);
  const [requirementCreated, setRequirementCreated] = useState<Record<string, boolean>>({});
  const [isSendingToTeam, setIsSendingToTeam] = useState(false);
  const [sentToTeam, setSentToTeam] = useState<Record<string, boolean>>({});


  // Get status icon based on record status
  const getStatusIcon = (status: PMORecordStatus) => {
    switch (status) {
      case 'Draft':
        return <PauseCircle className="w-5 h-5 text-gray-400" />;
      case 'In Progress':
        return <Clock className="w-5 h-5 text-blue-400" />;
      case 'Completed':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'Cancelled':
        return <XCircle className="w-5 h-5 text-red-400" />;
      default:
        return <AlertCircle className="w-5 h-5 text-yellow-400" />;
    }
  };

  // Get priority color class
  const getPriorityColorClass = (priority: PMORecordPriority) => {
    switch (priority) {
      case 'Low':
        return 'bg-green-500/20 text-green-400';
      case 'Medium':
        return 'bg-yellow-500/20 text-yellow-400';
      case 'High':
        return 'bg-orange-500/20 text-orange-400';
      case 'Critical':
        return 'bg-red-500/20 text-red-400';
      default:
        return 'bg-gray-500/20 text-gray-400';
    }
  };

  // Get team name from ID (memoized with useCallback as it's stable)
  const getTeamName = useCallback((teamId: AgenticTeamId): string => {
    switch (teamId) {
      case AgenticTeamId.Marketing:
        return 'Marketing';
      case AgenticTeamId.Research:
        return 'Research';
      case AgenticTeamId.SoftwareDesign:
        return 'Software Design';
      case AgenticTeamId.Sales:
        return 'Sales';
      case AgenticTeamId.BusinessAnalysis:
        return 'Business Analysis';
      default:
        return 'Unknown Team';
    }
  }, []);

  // Helper function to parse team name from assessment string
  const getProposedTeamNameFromAssessment = useCallback((assessment: string | undefined): string | null => {
    if (!assessment) return null;
    const teamsRegex = /\*\*Teams:\*\*\s*([A-Za-z\s]+?)\s*(?:\*\*Rationale:\*\*|$)/i;
    const match = assessment.match(teamsRegex);
    if (match && match[1]) {
      return match[1].trim();
    }
    return null;
  }, []);

  // Helper function to convert team name string to AgenticTeamId
  const getTeamIdFromName = useCallback((name: string): AgenticTeamId | null => {
    const normalizedName = name.trim().toLowerCase();
    const teamMappings: { [key: string]: AgenticTeamId } = {
      [getTeamName(AgenticTeamId.Marketing).toLowerCase()]: AgenticTeamId.Marketing,
      [getTeamName(AgenticTeamId.Research).toLowerCase()]: AgenticTeamId.Research,
      [getTeamName(AgenticTeamId.SoftwareDesign).toLowerCase()]: AgenticTeamId.SoftwareDesign,
      [getTeamName(AgenticTeamId.Sales).toLowerCase()]: AgenticTeamId.Sales,
      [getTeamName(AgenticTeamId.BusinessAnalysis).toLowerCase()]: AgenticTeamId.BusinessAnalysis,
    };
    return teamMappings[normalizedName] || null;
  }, [getTeamName]);


  // Combined logic to determine the actionable team for sending
  const getActionableTeamInfo = useCallback((record: PMORecord): { id: AgenticTeamId, name: string } | null => {
    // Priority 1: Parse from pmoAssessment
    // Apply fix here: use ?? undefined to handle potential null values
    const proposedTeamName = getProposedTeamNameFromAssessment(record.pmoAssessment ?? undefined);
    if (proposedTeamName) {
      const teamId = getTeamIdFromName(proposedTeamName);
      if (teamId) {
        return { id: teamId, name: getTeamName(teamId) };
      }
    }

    // Priority 2: Fallback to record.agentIds (first assigned team)
    if (record.agentIds && record.agentIds.length > 0) {
      const teamId = record.agentIds[0];
      const teamName = getTeamName(teamId);
      if (teamName !== 'Unknown Team') {
        return { id: teamId, name: teamName };
      }
    }
    return null;
  }, [getProposedTeamNameFromAssessment, getTeamIdFromName, getTeamName]);


  // Format date
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Toggle record expansion
  const toggleExpand = (recordId: string) => {
    setExpandedRecordId(expandedRecordId === recordId ? null : recordId);
  };

  // View record details
  const viewRecord = (recordId: string) => {
    router.push(`/services/pmo/${recordId}`);
  };

  // Create PMO Requirement document
  const createPMORequirement = async (record: PMORecord) => {
    if (isCreatingRequirement) return;
    setIsCreatingRequirement(true);

    try {
      if (!user?.email) throw new Error('User email not found');
      const currentDate = new Date().toISOString();
      const proposedTeamForDoc = "Marketing";

      const requirementsContent = `
# Requirements Specification
**Date:** ${currentDate}
**Project Title:** ${record.title}
**Project ID:** ${record.id}
## 1. Project Overview
${record.description}
## 2. PMO Assessment
${record.pmoAssessment || 'No assessment available.'}
## 3. Team Selection
${record.teamSelectionRationale || 'No team selection rationale available.'}
## 4. Assigned Teams
${record.agentIds && record.agentIds.length > 0
  ? record.agentIds.map(teamId => getTeamName(teamId)).join(', ')
  : 'No teams assigned'}
## 5. Requirements
- Requirement 1: TBD
- Requirement 2: TBD
- Requirement 3: TBD
## 6. Approval Section (Placeholder for Sign-off)
- **Project Manager:** ______________ Date: ______________
- **Lead(s) of Assigned Team(s) (${proposedTeamForDoc} Lead):** ______________ Date: ______________
- **Product Owner/SME:** ______________ Date: ______________
Proposed Team Delegation
**Teams:** ${proposedTeamForDoc} **Rationale:** The ${proposedTeamForDoc} Team is best suited for this task as it involves marketing strategy, content creation, brand management, and market analysis.
`;

      const response = await fetch('/api/pmo-process-document', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: `Requirements Specification - ${record.title} - (${record.id})`,
          content: requirementsContent,
          pmoId: record.id,
          userId: user.email,
          category: `PMO -  ${record.title} - ${record.id}`,
          metadata: {
            generatedAt: new Date().toISOString(),
            recordTitle: `${record.title} - ${record.id}`,
            recordDescription: record.description,
            recordStatus: record.status,
            recordPriority: record.priority,
          }
        }),
      });
      const result = await response.json();
      if (!response.ok || !result.success) throw new Error(result.error || 'Failed to create requirements document');

      // Update PMO record category to match document category
      try {
        const updateResponse = await fetch('/api/pmo-record-update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            recordId: record.id,
            category: `PMO -  ${record.title} - ${record.id}`,
          }),
        });
        if (!updateResponse.ok) {
          console.warn('Failed to update PMO record category, but document was created successfully');
        }
      } catch (updateError) {
        console.warn('Error updating PMO record category:', updateError);
      }

      setRequirementCreated(prev => ({ ...prev, [record.id]: true }));
      setSentToTeam(prev => ({ ...prev, [record.id]: false }));
      toast({
        title: "Requirements Document Created",
        description: "The requirements specification document has been created successfully.",
      });
    } catch (error) {
      console.error('Error creating requirements document:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create requirements document. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsCreatingRequirement(false);
    }
  };

  // Send requirements to delegated team (no longer creates strategic plan)
  const sendToDelegatedTeam = async (record: PMORecord) => {
    if (isSendingToTeam) return;

    const actionableTeam = getActionableTeamInfo(record);
    if (!actionableTeam) {
      toast({
        title: "Cannot Send",
        description: "No valid team could be determined for this action. Check PMO Assessment or assigned teams.",
        variant: "destructive",
      });
      return;
    }

    setIsSendingToTeam(true);

    try {
      if (!user?.email) throw new Error('User email not found');

      const { id: teamId, name: teamName } = actionableTeam;

      // Create team notification instead of strategic plan
      const response = await fetch('/api/pmo-notify-team', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pmoId: record.id,
          teamId: teamId,
          teamName: teamName,
          projectTitle: record.title,
          projectDescription: record.description,
          pmoAssessment: record.pmoAssessment,
          teamSelectionRationale: record.teamSelectionRationale,
          priority: record.priority,
          category: `PMO -  ${record.title} - ${record.id}`,
          userId: user.email,
          metadata: {
            notifiedAt: new Date().toISOString(),
            recordTitle: record.title,
            recordDescription: record.description,
            recordStatus: record.status,
            recordPriority: record.priority,
            assignedTeam: teamName,
            assignedTeamId: teamId,
            requiresStrategicPlan: true
          }
        }),
      });

      const result = await response.json();
      if (!response.ok || !result.success) throw new Error(result.error || 'Failed to notify team');

      // Update PMO record to mark as sent to team
      try {
        const updateResponse = await fetch('/api/pmo-record-update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            recordId: record.id,
            status: 'In Progress',
            assignedTeamNotified: true,
            notifiedAt: new Date().toISOString(),
          }),
        });
        if (!updateResponse.ok) {
          console.warn('Failed to update PMO record status, but team was notified successfully');
        }
      } catch (updateError) {
        console.warn('Error updating PMO record status:', updateError);
      }

      setSentToTeam(prev => ({ ...prev, [record.id]: true }));
      toast({
        title: "Requirements Sent to Team",
        description: `The requirements specification has been sent to the ${teamName} team. They will create their own strategic implementation plan.`,
      });
    } catch (error) {
      console.error('Error sending to delegated team:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send to delegated team. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSendingToTeam(false);
    }
  };

  // Delete record
  const confirmDelete = (recordId: string) => {
    setDeleteConfirmId(recordId);
  };

  const cancelDelete = () => {
    setDeleteConfirmId(null);
  };

  const handleDelete = async (recordId: string) => {
    setIsDeleting(true);
    try {
      if (!user?.email) throw new Error('User email not found');
      await deletePMORecord(user.email, recordId);
      setRequirementCreated(prev => { const newState = {...prev}; delete newState[recordId]; return newState; });
      setSentToTeam(prev => { const newState = {...prev}; delete newState[recordId]; return newState; });
      if (expandedRecordId === recordId) setExpandedRecordId(null);
      onRefresh();
      toast({ title: "Record Deleted", description: "The PMO record has been successfully deleted." });
    } catch (error) {
      console.error('Error deleting PMO record:', error);
      toast({ title: "Error Deleting", description: "Failed to delete PMO record.", variant: "destructive" });
    } finally {
      setIsDeleting(false);
      setDeleteConfirmId(null);
    }
  };

  return (
    <div className="space-y-4">
      {records.map(record => {
        const actionableTeam = getActionableTeamInfo(record);

        return (
          <div
            key={record.id}
            className="bg-gray-800 rounded-lg overflow-hidden border border-gray-700 hover:border-gray-600 transition-colors"
          >
            <div
              className="p-4 cursor-pointer"
              onClick={() => toggleExpand(record.id)}
            >
              <div className="flex flex-col md:flex-row md:items-center justify-between">
                <div className="flex items-start space-x-3">
                  {getStatusIcon(record.status)}
                  <div>
                    <h3 className="text-lg font-medium text-white">
                      {record.title}
                      <span className="text-sm text-gray-400 ml-2">- {record.id}</span>
                    </h3>
                    <p className="text-sm text-gray-400 mt-1 line-clamp-2">
                      {record.description}
                    </p>
                  </div>
                </div>
                <div className="flex items-center mt-3 md:mt-0">
                  <span className={`text-xs font-medium px-2 py-1 rounded-full ${getPriorityColorClass(record.priority)}`}>
                    {record.priority}
                  </span>
                  <ChevronRight
                    className={`w-5 h-5 text-gray-400 ml-2 transition-transform ${
                      expandedRecordId === record.id ? 'rotate-90' : ''
                    }`}
                  />
                </div>
              </div>
            </div>

            {expandedRecordId === record.id && (
              <div className="px-4 pb-4 pt-0 border-t border-gray-700">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Created</h4>
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                      <span className="text-sm text-white">
                        {formatDate(record.createdAt)}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Last Updated</h4>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 text-gray-500 mr-2" />
                      <span className="text-sm text-white">
                        {formatDate(record.updatedAt)}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Status</h4>
                    <div className="flex items-center">
                      {getStatusIcon(record.status)}
                      <span className="text-sm text-white ml-2">
                        {record.status}
                      </span>
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Assigned Teams</h4>
                    <div className="flex items-center">
                      <Users className="w-4 h-4 text-gray-500 mr-2" />
                      <span className="text-sm text-white">
                        {record.agentIds && record.agentIds.length > 0
                          ? record.agentIds.map(teamId => getTeamName(teamId)).join(', ')
                          : 'No teams assigned'}
                      </span>
                    </div>
                  </div>
                </div>

                {record.pmoAssessment && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-400 mb-1">PMO Assessment</h4>
                    <div className="text-sm text-gray-300 bg-gray-700/50 p-3 rounded-md prose prose-sm prose-invert max-w-none">
                      <MarkdownRenderer content={record.pmoAssessment ?? undefined} />
                    </div>
                  </div>
                )}

                {record.teamSelectionRationale && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-400 mb-1">Team Selection Rationale</h4>
                    <div className="text-sm text-gray-300 bg-gray-700/50 p-3 rounded-md prose prose-sm prose-invert max-w-none">
                      <MarkdownRenderer content={record.teamSelectionRationale ?? undefined} />
                    </div>
                  </div>
                )}

                <div className="flex flex-wrap gap-2 mt-4">
                  <button
                    onClick={() => viewRecord(record.id)}
                    className="flex items-center px-3 py-1.5 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors text-sm"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    View Details
                  </button>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => createPMORequirement(record)}
                      className="flex items-center px-3 py-1.5 bg-blue-600/30 text-blue-300 rounded-md hover:bg-blue-600/40 transition-colors text-sm"
                      disabled={isCreatingRequirement || (requirementCreated[record.id] && !sentToTeam[record.id])}
                    >
                      <FileText className="w-4 h-4 mr-1" />
                      {isCreatingRequirement ? 'Creating...' : (requirementCreated[record.id] ? 'Recreate Requirement' : 'Save PMO Requirement')}
                    </button>
                    {requirementCreated[record.id] && !sentToTeam[record.id] && (
                      <button
                        onClick={() => sendToDelegatedTeam(record)}
                        className="flex items-center px-3 py-1.5 bg-green-600/30 text-green-300 rounded-md hover:bg-green-600/40 transition-colors text-sm"
                        disabled={isSendingToTeam || !actionableTeam}
                      >
                        {isSendingToTeam ? (
                          <>
                            <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="w-4 h-4 mr-1" />
                            {actionableTeam
                              ? `Send to ${actionableTeam.name}`
                              : 'Send to Team'
                            }
                          </>
                        )}
                      </button>
                    )}
                  </div>
                  {deleteConfirmId === record.id ? (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-red-400">Confirm delete?</span>
                      <button
                        onClick={() => handleDelete(record.id)}
                        className="flex items-center px-3 py-1.5 bg-red-600/30 text-red-300 rounded-md hover:bg-red-600/40 transition-colors text-sm"
                        disabled={isDeleting}
                      >
                        {isDeleting ? 'Deleting...' : 'Yes'}
                      </button>
                      <button
                        onClick={cancelDelete}
                        className="flex items-center px-3 py-1.5 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors text-sm"
                      >
                        No
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={() => confirmDelete(record.id)}
                      className="flex items-center px-3 py-1.5 bg-red-600/30 text-red-300 rounded-md hover:bg-red-600/40 transition-colors text-sm"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Delete
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        )
      })}
    </div>
  );
};

export default PMORecordList;