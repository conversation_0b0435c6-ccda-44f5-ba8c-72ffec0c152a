// pages/services/pmo/index.tsx
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { PlusCircle, RefreshCw, Filter, Search, Sparkles } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { usePlanner } from '@/context/PlannerContext'; // Adjust path
import { PMORecord, PMORecordPriority, PMOFormInput } from 'lib/agents/pmo/PMOInterfaces'; // Adjust path
import { getPMORecords } from 'lib/firebase/pmo'; // Adjust path
import PMOFormNew from 'components/PMO/PMOFormNew'; // Import the actual form component
import PMORecordList from 'components/PMO/PMORecordList'; // Adjust path
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from 'components/ui/select';

// Firebase imports for fetching files/categories
import { collection, query, getDocs, where } from 'firebase/firestore';
import { db } from 'components/firebase';

// Types for files and categories
interface UserFile {
  id: string;
  name: string;
  category: string;
  namespace?: string;
}

interface UserCategory {
  id: string;
  name: string;
  documentCount?: number;
}


export default function PMOPage() {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const { projects, loading: plannerLoading, createProject, createTask } = usePlanner(); // Destructure methods if needed by agent handler

  const [pmoRecords, setPMORecords] = useState<PMORecord[]>([]);
  const [isLoadingRecords, setIsLoadingRecords] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [priorityFilter, setPriorityFilter] = useState<PMORecordPriority | 'all'>('all');

  // State for files and categories
  const [userFiles, setUserFiles] = useState<UserFile[]>([]);
  const [userCategories, setUserCategories] = useState<UserCategory[]>([]);
  const [isFetchingContextData, setIsFetchingContextData] = useState(true);

  const fetchPMORecords = useCallback(async () => {
    if (!user?.email) return;
    setIsLoadingRecords(true);
    try {
      const records = await getPMORecords(user.email);
      setPMORecords(records);
      setError(null);
    } catch (err: any) {
      console.error('Error fetching PMO records:', err);
      setError(err.message || 'Failed to fetch PMO records');
    } finally {
      setIsLoadingRecords(false);
    }
  }, [user]);

  const fetchUserContextData = useCallback(async () => {
    if (!user?.email) {
        setIsFetchingContextData(false);
        return;
    }
    setIsFetchingContextData(true);
    try {
        // Fetch Files (simplified example, adjust to your Firestore structure)
        const filesRef = collection(db, `users/${user.email}/files`);
        const filesQuery = query(filesRef); // Add any specific filtering if needed
        const filesSnapshot = await getDocs(filesQuery);
        const fetchedFiles: UserFile[] = filesSnapshot.docs.map(doc => ({
            id: doc.id, // Use Firestore doc ID as the file ID
            name: doc.data().name || 'Unnamed File',
            category: doc.data().category || 'Unknown', // Include category for display
        }));
        setUserFiles(fetchedFiles);

        // Fetch Categories (aggregate from files or from a dedicated categories collection)
        // This example aggregates categories from files, similar to AgentCollaborationTab
        const categoryMap = new Map<string, number>();
        fetchedFiles.forEach(file => {
            const categoryName = file.category || 'Uncategorized';
            if (categoryName !== 'Unknown') { // Example: Exclude 'Unknown' from selectable categories if desired
                categoryMap.set(categoryName, (categoryMap.get(categoryName) || 0) + 1);
            }
        });
        const fetchedCategories: UserCategory[] = Array.from(categoryMap.entries()).map(([name, count]) => ({
            id: name, // Use category name as ID if they are unique identifiers for selection
            name,
            documentCount: count,
        }));
        setUserCategories(fetchedCategories.sort((a,b) => a.name.localeCompare(b.name)));

    } catch (err) {
        console.error("Error fetching user context data:", err);
        // Handle error (e.g., show a message to the user)
    } finally {
        setIsFetchingContextData(false);
    }
  }, [user]);


  useEffect(() => {
    if (!authLoading && user) {
      fetchPMORecords();
      fetchUserContextData(); // Fetch context data when user is available
    }
  }, [authLoading, user, fetchPMORecords, fetchUserContextData]);

  const filteredRecords = pmoRecords.filter(record => {
    const recordTitle = record.title || "";
    const recordDescription = record.description || "";
    const lowerSearchQuery = searchQuery.toLowerCase();
    const matchesSearch =
      searchQuery === '' ||
      recordTitle.toLowerCase().includes(lowerSearchQuery) ||
      recordDescription.toLowerCase().includes(lowerSearchQuery);

    const matchesPriority =
      priorityFilter === 'all' ||
      record.priority === priorityFilter;

    return matchesSearch && matchesPriority;
  });

  const handleCreatePMORecord = () => {
    if (isFetchingContextData) {
        // Optionally show a small loader or disable button until context data is fetched
        console.log("Context data still fetching, please wait...");
        return;
    }
    setShowCreateForm(true);
  };

  const handleFormCancel = () => {
    setShowCreateForm(false);
  };

  const handleFormSubmit = async (formData: PMOFormInput) => {
    if (!user?.email) {
      setError("User not authenticated.");
      // Potentially show toast/alert
      return;
    }

    // Consider showing a global submitting overlay or disabling form interactions
    try {
      const response = await fetch('/api/pmo-agent-handler', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...formData, userId: user.email }),
      });

      if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to process PMO request');
      }

      // const result = await response.json(); // PMOAgentResult
      // console.log("PMO Agent Result:", result);

      setShowCreateForm(false);
      await fetchPMORecords();
      // Optionally show success toast
    } catch (err: any) {
      console.error('Error submitting PMO request:', err);
      setError(err.message || 'Failed to submit PMO request');
      // Potentially keep form open and show error within the form or above it, or use a toast
    }
  };

  if (authLoading || plannerLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-900">
        <div className="text-white text-xl flex items-center">
          <RefreshCw className="animate-spin mr-2" />
          Loading application data...
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-900 text-white p-6">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold mb-2 text-gray-100">Project Management Office (PMO)</h1>
            <p className="text-gray-400">
              Manage and coordinate tasks across five Agentic Teams.
            </p>
          </div>

          <div className="mt-4 md:mt-0">
            <button
              onClick={handleCreatePMORecord}
              disabled={isFetchingContextData} // Disable button while context data is loading
              className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <PlusCircle className="w-5 h-5 mr-2" />
              {isFetchingContextData ? "Loading Form..." : "Create PMO Request"}
            </button>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 mb-6 shadow-md">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search PMO records by title or description..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-purple-500 placeholder-gray-400"
              />
            </div>

            <div className="flex-shrink-0">
              <Select value={priorityFilter} onValueChange={(value) => setPriorityFilter(value as PMORecordPriority | 'all')}>
                <SelectTrigger className="w-full md:w-auto bg-gray-700 border-gray-600 text-white focus:ring-purple-500 focus:border-purple-500">
                  <SelectValue placeholder="All Priorities" />
                </SelectTrigger>
                <SelectContent className="bg-gray-800 text-white border-gray-600">
                  <SelectItem value="all" className="hover:!bg-purple-600 focus:!bg-purple-600">All Priorities</SelectItem>
                  <SelectItem value="Low" className="hover:!bg-purple-600 focus:!bg-purple-600">Low</SelectItem>
                  <SelectItem value="Medium" className="hover:!bg-purple-600 focus:!bg-purple-600">Medium</SelectItem>
                  <SelectItem value="High" className="hover:!bg-purple-600 focus:!bg-purple-600">High</SelectItem>
                  <SelectItem value="Critical" className="hover:!bg-purple-600 focus:!bg-purple-600">Critical</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <button
              onClick={() => {fetchPMORecords(); fetchUserContextData();}} // Refresh both records and context
              className="flex items-center justify-center px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white hover:bg-gray-600 transition-colors"
              title="Refresh records and context data"
            >
              <RefreshCw className="w-5 h-5 mr-2" />
              Refresh
            </button>
          </div>
        </div>

        {isLoadingRecords ? (
          <div className="flex justify-center items-center py-12">
            <RefreshCw className="w-8 h-8 animate-spin text-purple-400 mr-3" />
            <span className="text-lg text-gray-300">Loading PMO records...</span>
          </div>
        ) : error ? (
          <div className="bg-red-800/30 border border-red-600 rounded-lg p-6 text-center shadow-lg">
            <p className="text-red-300 text-lg mb-3">{error}</p>
            <button
              onClick={fetchPMORecords}
              className="mt-2 px-4 py-2 bg-red-600 rounded-md text-white hover:bg-red-500 transition-colors"
            >
              Try Again
            </button>
          </div>
        ) : filteredRecords.length === 0 && !searchQuery && priorityFilter === 'all' ? (
          <div className="bg-gray-800 rounded-lg p-10 text-center shadow-xl border border-gray-700">
            <Sparkles className="w-16 h-16 text-purple-400 mx-auto mb-5" />
            <h3 className="text-2xl font-semibold mb-3 text-gray-100">No PMO Records Found</h3>
            <p className="text-gray-400 mb-6 text-md">
              It looks like there are no PMO requests yet. Get started by creating one!
            </p>
            <button
              onClick={handleCreatePMORecord}
              disabled={isFetchingContextData}
              className="px-6 py-3 bg-purple-600 rounded-md text-white hover:bg-purple-700 transition-colors text-lg font-medium disabled:opacity-50"
            >
              Create Your First PMO Request
            </button>
          </div>
        ) :  filteredRecords.length === 0 && (searchQuery || priorityFilter !== 'all') ? (
            <div className="bg-gray-800 rounded-lg p-10 text-center shadow-xl border border-gray-700">
              <Search className="w-16 h-16 text-gray-500 mx-auto mb-5" />
              <h3 className="text-2xl font-semibold mb-3 text-gray-100">No Matching Records</h3>
              <p className="text-gray-400 mb-6 text-md">
                No PMO records match your current search query or priority filter.
              </p>
              <button
                onClick={() => { setSearchQuery(''); setPriorityFilter('all'); }}
                className="px-5 py-2 bg-gray-600 rounded-md text-white hover:bg-gray-500 transition-colors font-medium"
              >
                Clear Filters
              </button>
            </div>
        ) : (
          <PMORecordList records={filteredRecords} onRefresh={fetchPMORecords} />
        )}
      </div>

      {showCreateForm && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 p-4 overflow-y-auto">
          <div className="bg-gray-800 rounded-lg shadow-2xl max-w-3xl w-full max-h-[95vh] border border-gray-700">
             <div className="flex justify-between items-center p-6 border-b border-gray-700">
                <h2 className="text-2xl font-bold text-white">Create New PMO Request</h2>
                <button onClick={handleFormCancel} className="text-gray-400 hover:text-white transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" /></svg>
                </button>
             </div>
            <div className="p-6 overflow-y-auto custom-scrollbar" style={{maxHeight: 'calc(95vh - 80px)'}}> {/* Adjust 80px based on header height */}
              <PMOFormNew
                projects={projects}
                userFiles={userFiles}
                userCategories={userCategories}
                isFetchingFilesCategories={isFetchingContextData}
                onSubmit={handleFormSubmit}
                onCancel={handleFormCancel}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}