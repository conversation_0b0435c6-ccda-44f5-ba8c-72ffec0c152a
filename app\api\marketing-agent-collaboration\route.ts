/**
 * Marketing Agent Collaboration API
 *
 * This API endpoint orchestrates collaboration between multiple agents to provide
 * comprehensive marketing strategy analysis and recommendations.
 *
 * The workflow:
 * 1. Strategic Director analyzes the request and searches for relevant documents
 * 2. Question Answer Agent extracts specific information from documents
 * 3. Strategic Director synthesizes a final response
 */

import { NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { StrategicDirectorAgent } from '../../../lib/agents/marketing/StrategicDirectorAgent';
import { LlmProvider } from '../../../lib/tools/llm-tool';
import { adminDb } from '../../../components/firebase-admin';
import { getServerSession } from 'next-auth';
import { authOptions } from '../auth/[...nextauth]/authOptions';

// Define the request body interface for TypeScript type checking
type RequestBody = {
  prompt: string;
  modelProvider?: string | LlmProvider;
  modelName?: string;
  context?: string;
  documentReferences?: string[];
  category?: string;
  userId?: string;
}

// Define the agent message interface
interface AgentMessage {
  from: string;
  to: string;
  message: string;
  thinking?: string;
  timestamp: Date;
}

export async function POST(req: Request) {
  try {
    // Parse the request body
    const body = await req.json() as RequestBody;

    // Extract parameters from the request
    const {
      prompt,
      modelProvider = 'openai' as LlmProvider,
      modelName = 'gpt-4o',
    } = body;

    // Validate prompt
    if (!prompt || typeof prompt !== 'string' || prompt.trim().length === 0) {
      return NextResponse.json(
        { error: 'A valid prompt is required' },
        { status: 400 }
      );
    }

    // Get the user's session for authentication
    const session = await getServerSession(authOptions);

    // Determine if we're in a development/testing environment
    const isDevelopment = process.env.NODE_ENV === 'development';

    let userId: string;

    if (session?.user?.email) {
      // Use the authenticated user's email if available
      userId = session.user.email;
      console.log(`Using authenticated user ID: ${userId}`);
    } else if (isDevelopment && body.userId) {
      // In development, allow a user ID from the request body with a warning
      userId = body.userId;
      console.warn(`⚠️ WARNING: Using user ID from request body (${userId}) because no authenticated user was found.`);
      console.warn(`⚠️ This is only allowed in development/testing environments.`);
    } else if (isDevelopment) {
      // In development, allow a fallback user ID with a clear warning
      userId = 'test-user-' + Date.now();
      console.warn(`⚠️ WARNING: Using fallback test user ID (${userId}) because no authenticated user was found.`);
      console.warn(`⚠️ This is only allowed in development/testing environments.`);
    } else {
      // In production, require authentication
      console.error('Authentication required: No user session found in production environment');
      return NextResponse.json({ error: 'Unauthorized. User must be authenticated.' }, { status: 401 });
    }

    console.log(`Processing marketing agent collaboration request for user ${userId}`);
    console.log(`Prompt: "${prompt}"`);
    console.log(`Model: ${modelProvider}/${modelName}`);

    // Log context options for debugging
    console.log(`Context options:`, {
      customContext: body.context || null,
      documentReferences: body.documentReferences || null,
      category: body.category || null
    });

    // Initialize the conversation array
    const conversation: AgentMessage[] = [];

    // Declare queryResult at the top level of the function
    let queryResult: any = null;

    // Create the Strategic Director Agent with the user's ID
    const strategicDirector = new StrategicDirectorAgent(
      'strategic-director',
      'Strategic Director',
      userId,
      modelProvider as LlmProvider,
      modelName
    );

    // Prepare document query options
    let queryOptions: any = {
      useInternetSearch: true // Use internet search as fallback
    };

    // Default to marketing category only if no specific context is provided
    if (!body.context && !body.documentReferences && !body.category) {
      queryOptions.category = 'marketing';
    }

    // Initialize document query
    let documentQuery = `Find information related to: ${prompt}`;

    /**
     * Helper function to fetch namespaces for a category
     */
    async function fetchNamespacesForCategory(userId: string, category: string): Promise<string[]> {
      try {
        console.log(`Fetching namespaces for category ${category}`);

        // Query files collection for documents with the specified category
        const filesSnapshot = await adminDb.collection('users')
          .doc(userId)
          .collection('files')
          .where('category', '==', category)
          .get();

        // Extract namespaces from the query results
        const namespaces = filesSnapshot.docs
          .map(doc => doc.data().namespace)
          .filter(Boolean);

        return namespaces;
      } catch (error) {
        console.error(`Error fetching namespaces for category ${category}:`, error);
        return [];
      }
    }

    // If a category is specified, use it
    if (body.category) {
      queryOptions.category = body.category;
      // Correct document query format for vector-based semantic search
      documentQuery = `Find information related to: "${prompt}" from the document content`;
      queryOptions.useVectorSearch = true;

      try {
        // Import necessary tools and utilities
        const { OpenAIEmbeddings } = await import("@langchain/openai");
        const { ProcessContentTool } = await import("../../../components/tools/ProcessContentTool");
        const { TokenManagement } = await import("@/src/tokenTracker/tokenManagement");

        // 1. Convert prompt to vector embedding
        console.log(`Converting prompt to vector embedding: "${prompt}"`);
        const embeddings = new OpenAIEmbeddings();
        const queryVector = await embeddings.embedQuery(prompt);

        // 2. Get namespaces for the category
        console.log(`Fetching namespaces for category: ${body.category}`);
        const namespaces = await fetchNamespacesForCategory(userId, body.category);

        if (namespaces && namespaces.length > 0) {
          console.log(`Found ${namespaces.length} namespaces for category ${body.category}`);

          // 3. Initialize the ProcessContentTool with a mock analytics middleware
          const mockAnalytics = {
            track: async () => {},
            interceptQueryStart: async () => "mock-query-id",
            interceptQueryEnd: async () => {},
            interceptChatHistoryMetrics: async () => {},
            interceptTokenAnalysis: async () => {},
            interceptContentProcessing: async () => {}
          };
          const tokenManager = new TokenManagement(mockAnalytics);
          const contentTool = new ProcessContentTool(userId, tokenManager);

          // 4. Process content using vector similarity
          console.log(`Processing content using vector similarity with ${namespaces.length} namespaces`);
          const contentResult = await contentTool._call({
            queryVector,
            namespaces,
            userId
          });

          // 5. Use the result as the query result
          if (contentResult.success) {
            console.log(`Vector similarity search successful with ${contentResult.metadata.chunkCount} chunks`);
            queryResult = {
              success: true,
              content: contentResult.content,
              sources: contentResult.metadata.sources.map(source => ({
                title: source.title,
                content: source.doc_id
              })),
              metadata: {
                totalTokens: contentResult.metadata.totalTokens,
                chunkCount: contentResult.metadata.chunkCount,
                averageRelevance: contentResult.metadata.averageRelevance,
                namespaceDistribution: contentResult.metadata.namespaceDistribution,
                internetSearchUsed: false,
                functionCallingUsed: false
              }
            };

            // Skip the standard document query since we've already processed the content
            console.log(`Using vector similarity results instead of standard document query`);
          } else {
            console.warn(`Vector similarity search failed: ${contentResult.error}`);
            // Fall back to the standard approach if vector similarity fails
            console.log(`Falling back to standard document query`);
          }
        } else {
          console.warn(`No namespaces found for category: ${body.category}`);
        }
      } catch (error) {
        console.error("Error in vector similarity search:", error);
        // Continue with standard document query as fallback
        console.log(`Falling back to standard document query due to error: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // If document references are specified, use them
    if (body.documentReferences && body.documentReferences.length > 0) {
      const documentNamespace = body.documentReferences[0]; // Use the first document for now

      // Look up the actual filename for this namespace
      try {
        const filesRef = adminDb.collection('users')
          .doc(userId)
          .collection('files')
          .where('namespace', '==', documentNamespace)
          .limit(1);

        const fileSnapshot = await filesRef.get();

        if (!fileSnapshot.empty) {
          const fileData = fileSnapshot.docs[0].data();
          const fileName = fileData.name;

          // Use the actual filename instead of the namespace
          queryOptions.filename = fileName;
          documentQuery = `Find information related to: "${prompt}" in the document "${fileName}".`;
          console.log(`Using document reference: ${documentNamespace} (filename: ${fileName})`);
        } else {
          // If we can't find the file, use the namespace directly
          queryOptions.filename = documentNamespace;
          documentQuery = `Find information related to: "${prompt}" in the document "${documentNamespace}".`;
          console.log(`Using document reference: ${documentNamespace} (no filename found)`);
        }
      } catch (error) {
        console.error(`Error looking up filename for namespace ${documentNamespace}:`, error);
        // Fallback to using the namespace directly
        queryOptions.filename = documentNamespace;
        documentQuery = `Find information related to: "${prompt}" in the document "${documentNamespace}".`;
        console.log(`Using document reference: ${documentNamespace} (error looking up filename)`);
      }

      // If we have a document reference, we should not use a category
      // This ensures the document search takes precedence
      queryOptions.category = undefined;
    }

    // Add document query to conversation
    conversation.push({
      from: 'strategic-director',
      to: 'query-documents',
      message: documentQuery,
      timestamp: new Date()
    });

    // This line is no longer needed as queryResult is declared at the top of the function

    // Log query parameters for debugging
    console.log(`Executing query with parameters:`, {
      query: documentQuery,
      category: queryOptions.category || null,
      filename: queryOptions.filename || null,
      useInternetSearch: queryOptions.useInternetSearch || false
    });

    // Use the enhanced document content extractor for all document queries
    console.log(`Using enhanced document content extractor for query`);

    if (body.documentReferences && body.documentReferences.length > 0) {
      // Get the namespace from the document references
      const namespace = body.documentReferences[0];
      console.log(`Using document reference with namespace: ${namespace}`);

      // Use the enhanced document querying method with the namespace
      try {
        console.log(`Querying documents with namespace: ${namespace}, model: ${modelName}, provider: ${modelProvider}`);

        // Check if the document query is likely to be complex or large
        // Lowering the thresholds to make more queries use the async path
        const isComplexQuery = documentQuery.length > 200 ||
                              prompt.length > 300 ||
                              (queryOptions.filename && queryOptions.filename.endsWith('.pdf')) ||
                              (queryOptions.category === 'Research' || queryOptions.category === 'Reports') ||
                              prompt.toLowerCase().includes('strategic') ||
                              prompt.toLowerCase().includes('comprehensive') ||
                              prompt.toLowerCase().includes('analysis');

        if (isComplexQuery && process.env.USE_ASYNC_PROCESSING === 'true') {
          // For complex queries, use the asynchronous document processing queue
          console.log(`Using asynchronous document processing for complex query: "${documentQuery.substring(0, 100)}..."`);

          // Submit the job to the document processing queue using internal API authentication
          // Log the internal API secret (masked) for debugging
          const internalSecret = process.env.INTERNAL_API_SECRET || '';
          console.log(`Internal API Secret configured: ${internalSecret ? 'Yes (length: ' + internalSecret.length + ')' : 'No'}`);

          // Ensure we have a valid URL
          const apiUrl = process.env.NEXTAUTH_URL
            ? `${process.env.NEXTAUTH_URL}/api/document-processing`
            : 'http://localhost:3000/api/document-processing';

          console.log(`Submitting document processing job to: ${apiUrl}`);

          const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-Internal-Auth': internalSecret,
              'X-Acting-As-User-Id': userId, // Pass the user ID for authorization
            },
            body: JSON.stringify({
              documentQuery,
              category: queryOptions.category,
              filename: queryOptions.filename,
              namespace,
              useInternetSearch: queryOptions.useInternetSearch || false,
              modelName: 'gemini-2.5-pro-preview-05-06', // Use provider/model format
              userId: userId // Include userId in the body as well for backward compatibility
            })
          });

          if (!response.ok) {
            throw new Error(`Failed to submit document processing job: ${response.status} ${response.statusText}`);
          }

          const jobData = await response.json();

          // Return a response indicating the job is being processed asynchronously
          return NextResponse.json({
            success: true,
            message: 'Document query is being processed asynchronously',
            jobId: jobData.jobId,
            status: jobData.status,
            isAsync: true
          });
        }

        // For simpler queries, process synchronously with a timeout
        // Set a timeout for the document query operation
        // Use Gemini 2.5 Pro as the default model for document querying
        const queryPromise = strategicDirector.queryDocumentsEnhanced(
          documentQuery,
          queryOptions.category,
          queryOptions.filename,
          namespace,
          queryOptions.useInternetSearch || false,
          'gemini-2.5-pro-preview-05-06' // Use provider/model format to avoid confusion
        );

        // Create a timeout promise with significantly increased timeout (900 seconds)
        const timeoutPromise = new Promise<any>((_, reject) => {
          setTimeout(() => {
            reject(new Error('Document query operation timed out after 900 seconds (15 minutes)'));
          }, 900000); // 900 second timeout (15 minutes)
        });

        // Race the query against the timeout
        queryResult = await Promise.race([queryPromise, timeoutPromise]);

      } catch (queryError) {
        console.error(`Error querying documents with namespace ${namespace}:`, queryError);

        // Implement the new fallback chain
        console.log(`Starting fallback chain for document query`);

        try {
          // First fallback: OpenAI o3
          console.log(`Attempting first fallback with OpenAI o3...`);
          queryResult = await strategicDirector.queryDocumentsEnhanced(
            documentQuery,
            queryOptions.category,
            queryOptions.filename,
            namespace,
            queryOptions.useInternetSearch || false,
            'openai/o3-2025-04-16' // Use provider/model format to avoid confusion
          );
        } catch (firstFallbackError) {
          // If OpenAI fails, try with Claude 3.7
          console.error(`OpenAI fallback failed:`, firstFallbackError);
          console.log(`Attempting second fallback with Claude 3.7 Sonnet...`);

          try {
            queryResult = await strategicDirector.queryDocumentsEnhanced(
              documentQuery,
              queryOptions.category,
              queryOptions.filename,
              namespace,
              queryOptions.useInternetSearch || false,
              'anthropic/claude-3-7-sonnet' // Use provider/model format to avoid confusion
            );
          } catch (secondFallbackError) {
            // If Claude also fails, try with DeepSeek as last resort
            console.error(`Claude fallback failed:`, secondFallbackError);
            console.log(`Attempting final fallback with DeepSeek Coder...`);

            queryResult = await strategicDirector.queryDocumentsEnhanced(
              documentQuery,
              queryOptions.category,
              queryOptions.filename,
              namespace,
              queryOptions.useInternetSearch || false,
              'groq/deepseek-coder' // Use provider/model format to avoid confusion
            );
          }
        }
      }
    } else {
      // Use the enhanced document querying method for category-based queries
      // Use Gemini 2.5 Pro as the default model for document querying
      queryResult = await strategicDirector.queryDocumentsEnhanced(
        documentQuery,
        queryOptions.category,
        queryOptions.filename,
        undefined,
        queryOptions.useInternetSearch || false,
        'gemini-2.5-pro-preview-05-06' // Use provider/model format to avoid confusion
      );
    }

    // If we have documents or the query was successful, proceed with normal processing
    // Step 2: Strategic Director initial analysis
    console.log('Step 2: Strategic Director initial analysis');

    // Pass the context from QueryDocumentsAgent to the Strategic Director's thinking process
    const queryContext = queryResult.success ? queryResult.content : '';
    const strategicThinking = await strategicDirector.getThinking(prompt, queryContext);
    const initialResponse = await strategicDirector.processRequest(prompt, queryContext);

    conversation.push({
      from: 'strategic-director',
      to: 'user',
      message: initialResponse,
      thinking: strategicThinking,
      timestamp: new Date()
    });

    // Add the document query result to the conversation
    conversation.push({
      from: 'query-documents',
      to: 'strategic-director',
      message: queryResult.success
        ? `Found the following information:\n\n${queryResult.content}`
        : queryResult.content || 'No relevant documents found.',
      timestamp: new Date()
    });

    // Step 3: Strategic Director formulates questions
    console.log('Step 3: Strategic Director formulates questions');

    // Create a question context
    let questionContext = `
Based on the user request: "${prompt}" and the document search results, I need to answer some key questions.

${body.category ? `The documents are from the category "${body.category}".` : ''}

Please identify 3-5 key questions that would help extract the most relevant information, and then answer those questions based on the document content.
`;

    conversation.push({
      from: 'strategic-director',
      to: 'question-answer',
      message: questionContext,
      timestamp: new Date()
    });

    // Step 4: Question Answer Agent processes the request
    console.log('Step 4: Question Answer Agent processes the request');

    // Prepare context for question answering
    let context = queryResult.content;

    // Add custom context if provided
    if (body.context) {
      context = `${body.context}\n\n${context}`;
    }

    // Create the question answer request
    const questionResult = await strategicDirector.answerQuestion(
      prompt,
      context,
      queryOptions.category || 'marketing',
      {
        internetSearch: true
      }
    );

    // Add the questions and answers to the conversation
    if (questionResult.questions && questionResult.questions.length > 0) {
      questionResult.questions.forEach((qa: any, index: number) => {
        conversation.push({
          from: 'question-answer',
          to: 'strategic-director',
          message: `Question ${index + 1}: ${qa.question}\n\nAnswer: ${qa.answer || 'No answer found.'}`,
          timestamp: new Date(Date.now() + index * 1000) // Stagger timestamps for display
        });
      });
    }

    // Step 5: Strategic Director synthesizes final response
    console.log('Step 5: Strategic Director synthesizes final response');

    // Prepare final prompt with context information
    let contextInfo = '';
    if (body.context) {
      contextInfo = `\nUser-provided context: "${body.context}"`;
    } else if (body.documentReferences && body.documentReferences.length > 0) {
      // Get the actual filenames for the document references
      const documentNames: string[] = [];

      for (const namespace of body.documentReferences) {
        try {
          const filesRef = adminDb.collection('users')
            .doc(userId)
            .collection('files')
            .where('namespace', '==', namespace)
            .limit(1);

          const fileSnapshot = await filesRef.get();

          if (!fileSnapshot.empty) {
            const fileData = fileSnapshot.docs[0].data();
            documentNames.push(fileData.name || namespace);
          } else {
            documentNames.push(namespace);
          }
        } catch (error) {
          console.error(`Error looking up filename for namespace ${namespace}:`, error);
          documentNames.push(namespace);
        }
      }

      contextInfo = `\nUser selected document(s): "${documentNames.join('", "')}"`;
    } else if (body.category) {
      contextInfo = `\nUser selected category: "${body.category}"`;
    } else {
      contextInfo = `\nNo specific context was provided by the user.`;
    }

    const finalPrompt = `
Based on all the information gathered:
1. User request: "${prompt}"${contextInfo}
2. Document search results: ${queryResult.success
  ? queryResult.content.substring(0, 500) + '...'
  : 'No relevant documents found. ' + (queryResult.content || '')}
3. Questions and answers: ${JSON.stringify(questionResult.questions.map((qa: any) => ({ question: qa.question, answer: qa.answer })))}

Provide a comprehensive, strategic marketing response that addresses the user's request.
`;

    const finalThinking = await strategicDirector.getThinking(finalPrompt, '');
    const finalResponse = await strategicDirector.processRequest(finalPrompt, '');

    conversation.push({
      from: 'strategic-director',
      to: 'user',
      message: finalResponse,
      thinking: finalThinking,
      timestamp: new Date()
    });

    // Generate a unique request ID
    const requestId = uuidv4();

    // Extract the final response from the conversation
    const finalResponseMsg = conversation.find(
      (msg) => msg.from === 'strategic-director' && msg.to === 'user' && msg.thinking
    );

    // Prepare data to be stored in Firestore
    const agentOutputData = {
      requestId,
      timestamp: new Date(),
      agentType: 'strategic-director',
      prompt,
      result: {
        thinking: finalResponseMsg?.thinking || '',
        output: finalResponseMsg?.message || 'No response generated.',
        documentUrl: null // Strategic analysis doesn't generate a document URL in the same way
      },
      agentMessages: conversation.map((msg) => ({
        from: msg.from,
        to: msg.to,
        message: msg.message
      })),
      modelInfo: {
        provider: modelProvider,
        model: modelName
      }
    };

    // Store the data in Firestore
    try {
      console.log(`[AGENT_OUTPUT] Storing strategic analysis output with requestId: ${requestId}`);
      await adminDb.collection('Agent_Output').doc(requestId).set(agentOutputData);
      console.log(`[AGENT_OUTPUT] Successfully stored strategic analysis output with requestId: ${requestId}`);
    } catch (error) {
      console.error(`[AGENT_OUTPUT] Error storing strategic analysis output:`, error);
      // Continue with the response even if storage fails
    }

    // Return the result
    return NextResponse.json(
      {
        requestId,
        conversation,
        modelInfo: {
          provider: modelProvider,
          model: modelName
        },
        contextOptions: {
          customContext: body.context || null,
          documentReferences: body.documentReferences || null,
          category: body.category || null
        }
      },
      {
        status: 200,
        headers: {
          'X-Job-Id': requestId
        }
      }
    );

  } catch (error) {
    console.error('Error processing marketing agent collaboration request:', error);

    // Provide more detailed error information
    let errorMessage = 'Failed to process collaboration request';
    let errorDetails = error instanceof Error ? error.message : String(error);

    return NextResponse.json(
      {
        error: errorMessage,
        details: errorDetails,
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}






