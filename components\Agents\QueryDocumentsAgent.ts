/**
 * QueryDocumentsAgent.ts
 *
 * This agent provides enhanced document querying capabilities by coordinating multiple tools:
 * - queryDocumentsTool: For retrieving document content based on queries
 * - generateFollowUpQuestionsTool: For suggesting follow-up questions
 * - findRelatedCategoriesTool: For discovering related document categories
 * - findInternalDocumentsTool: For searching for documents by name or category
 * - internetSearchTool: For searching the web when document content is insufficient
 * - markdownRendererTool: For formatting and processing markdown content
 * - genericDocumentContentExtractorTool: For comprehensive analysis of provided document content
 * - enhancedDocumentContentExtractorTool: For retrieving, analyzing, and summarizing content from specified documents or categories
 *
 * The agent uses function calling to invoke these tools as needed based on the context.
 */

import { queryDocumentsTool, QueryDocumentsOptions, QueryDocumentsResult } from '../../lib/tools/queryDocumentsTool';
import { generateFollowUpQuestionsTool } from '../../lib/tools/generateFollowUpQuestionsTool';
import { findRelatedCategoriesTool, CategoryInfo } from '../../lib/tools/findRelatedCategoriesTool';
import { findInternalDocumentsTool, DocumentInfo } from '../../lib/tools/findInternalDocumentsTool';
import { internetSearchTool } from '../../lib/tools/internetSearchTool';
import { markdownRendererTool } from '../../lib/tools/markdown-renderer-tool';
// Added imports for new tools
import { genericDocumentContentExtractorTool } from '../../lib/tools/genericDocumentContentExtractorTool';
import { enhancedDocumentContentExtractorTool } from '../../lib/tools/enhancedDocumentContentExtractorTool';
import { TokenManagement } from '@/src/tokenTracker/tokenManagement';
// import { processWithGroq } from '../../lib/tools/groq-ai'; // No longer needed as we're using document extractor tools
import { processWithGoogleAI } from '../../lib/tools/google-ai';
import { requestForInformationTool } from '../../lib/tools/requestForInformationTool';
import { QueryContextAnalysisSchema, safeParseJson } from '../../lib/schemas/llmResponseSchemas';
import { adminDb } from '../../components/firebase-admin';

export interface QueryDocumentsAgentConfig {
  tokenManager?: TokenManagement;
  maxResults?: number;
  defaultTemperature?: number;
  defaultMaxTokens?: number;
  userId?: string;
  includeExplanation?: boolean;
}

export interface QueryDocumentsAgentResult extends QueryDocumentsResult {
  followUpQuestions?: string[];
  relatedCategories?: CategoryInfo[];
  matchingDocuments?: DocumentInfo[];
  // Potentially, future enhancements could add specific fields from new tools if needed
  // For now, their output is primarily synthesized into the main 'content' by the LLM
  metadata?: {
    chunkCount?: number;
    averageRelevance?: number;
    internetSearchUsed?: boolean;
    functionCallingUsed?: boolean;
    namespaces?: string[];
    category?: string;
    extractionToolUsed?: string; // Added for document extraction tool tracking
    requestForInformation?: {
      message: string;
      questions: string[];
      actions: string[];
    };
  };
}

export class QueryDocumentsAgent {
  private maxResults: number;
  private defaultTemperature: number;
  private defaultMaxTokens: number;

  constructor(config: QueryDocumentsAgentConfig = {}) {
    // Initialize with provided configurations
    this.maxResults = config.maxResults || 10;
    this.defaultTemperature = config.defaultTemperature || 0.3;
    this.defaultMaxTokens = config.defaultMaxTokens || 2000;
  }

  /**
   * Get the tool definitions for function calling
   * @returns Array of tool definitions
   */
  getToolDefinitions() {
    return [
      queryDocumentsTool.getToolDefinition(),
      generateFollowUpQuestionsTool.getToolDefinition(),
      findRelatedCategoriesTool.getToolDefinition(),
      findInternalDocumentsTool.getToolDefinition(),
      internetSearchTool.getToolDefinition(),
      markdownRendererTool.getToolDefinition(),
      genericDocumentContentExtractorTool.getToolDefinition(), // Added
      enhancedDocumentContentExtractorTool.getToolDefinition() // Added
    ];
  }

  /**
   * Main entry point for processing document queries with enhanced capabilities
   *
   * @param options - Query options including user query and category/filename
   * @returns Enhanced query results with additional metadata
   */
  /**
   * Analyze query context using Gemini 2.0 Flash
   * This method determines if the query has enough context to search for documents
   *
   * @param query - The user's query
   * @param context - Optional context information including filename and category
   * @returns Analysis result with context sufficiency and recommendations
   */
  async analyzeQueryContext(
    query: string,
    context?: {
      filename?: string;
      category?: string;
    }
  ): Promise<{
    hasEnoughContext: boolean;
    missingContext: string[];
    enhancedQuery?: string;
    recommendation: string;
  }> {
    try {
      console.log(`QueryDocumentsAgent: Analyzing query context with Gemini: "${query}"${context?.filename ? ` for file "${context.filename}"` : ''}`);

      // If a filename or category is provided, we can automatically consider the query to have enough context
      // This is important for filename-based or category-based requests where the query might be generic
      if (context?.filename) {
        console.log(`QueryDocumentsAgent: Filename provided (${context.filename}), considering query to have enough context`);
        return {
          hasEnoughContext: true,
          missingContext: [],
          recommendation: `I'll search for information in the file "${context.filename}".`
        };
      }

      // For category-based queries, also consider them to have enough context
      if (context?.category) {
        console.log(`QueryDocumentsAgent: Category provided (${context.category}), considering query to have enough context`);
        return {
          hasEnoughContext: true,
          missingContext: [],
          recommendation: `I'll search for information in the "${context.category}" category.`
        };
      }

      const prompt = `
You are an expert document search assistant. Your task is to analyze the following query and determine if it has enough context to effectively search for documents.

Query: "${query}"
${context?.category ? `\nCategory: "${context.category}"` : ''}

Please analyze this query and determine:
1. Does this query have enough specific context to effectively search for documents? (true/false)
2. What specific context is missing? (list as bullet points)
3. How could this query be enhanced to be more effective? (provide a specific enhanced version)
4. What recommendation would you give to the user to improve their search? (be specific and helpful)

Respond in JSON format with the following structure:
{
  "hasEnoughContext": boolean,
  "missingContext": [string array of missing context elements],
  "enhancedQuery": string (improved version of the query),
  "recommendation": string (helpful guidance for the user)
}
`;

      const response = await processWithGoogleAI({
        prompt,
        model: "gemini-2.0-flash"
      });

      // Use the safeParseJson function with our Zod schema
      const result = safeParseJson(QueryContextAnalysisSchema, response, {
        hasEnoughContext: true,
        missingContext: [],
        recommendation: "I'll search for documents based on your query."
      });

      // Ensure we return the correct type with default values for required fields
      return {
        hasEnoughContext: result.hasEnoughContext ?? true,
        missingContext: result.missingContext ?? [],
        enhancedQuery: result.enhancedQuery,
        recommendation: result.recommendation ?? "I'll search for documents based on your query."
      };
    } catch (error) {
      console.error("Error analyzing query context with Gemini:", error);
      // Default to allowing the search to proceed if there's an error
      return {
        hasEnoughContext: true,
        missingContext: [],
        recommendation: "I'll search for documents based on your query."
      };
    }
  }

  /**
   * Process documents to extract information relevant to the query
   * This method determines whether to use genericDocumentContentExtractorTool or enhancedDocumentContentExtractorTool
   * based on whether there's a single document or multiple documents
   *
   * @param query - The original user query
   * @param queryResult - The result from queryDocumentsTool
   * @param category - Optional document category
   * @param filename - Optional specific filename
   * @param model - Optional model name to use
   * @param modelOptions - Optional model options
   * @returns Processed query results with relevant content
   */
  private async processDocumentsForQuery(
    query: string,
    queryResult: QueryDocumentsAgentResult,
    userId: string,
    category?: string,
    filename?: string,
    model?: string,
    modelOptions?: Record<string, any>
  ): Promise<QueryDocumentsAgentResult> {
    try {
      // Determine if we're dealing with a single document or multiple documents
      const isSingleDocument = filename !== undefined || category === 'Unknown' || category === undefined;

      console.log(`QueryDocumentsAgent: Processing ${isSingleDocument ? 'single' : 'multiple'} document(s) for query "${query}"`);

      if (isSingleDocument) {
        // For single documents, use the more efficient genericDocumentContentExtractorTool
        console.log(`QueryDocumentsAgent: Using genericDocumentContentExtractorTool for single document processing`);

        // Create a focused extraction prompt that only extracts information relevant to the query
        console.log(`QueryDocumentsAgent: Enabling vector similarity for query "${query}"`);

        // Get the namespace from the metadata if available
        const namespace = queryResult.metadata?.namespaces?.[0];
        console.log(`QueryDocumentsAgent: Using namespace "${namespace || 'none'}" for vector similarity`);

        const extractionResult = await genericDocumentContentExtractorTool.process({
          documentContent: queryResult.content,
          documentTitle: filename || 'Query Result',
          userQuery: query,
          performVectorSimilarity: true, // Enable vector similarity for better results
          userId: userId,
          modelName: model || "deepseek-r1-distill-llama-70b",
          modelOptions: modelOptions, // Pass model options if provided
          category: category, // Pass the category for category-based vector similarity
          documentId: namespace // Pass the namespace as documentId for vector similarity
        });

        if (!extractionResult.success) {
          console.error(`QueryDocumentsAgent: Document extraction failed: ${extractionResult.error}`);
          return queryResult; // Return original result if extraction fails
        }

        // Return the extracted content focused on the query
        return {
          success: true,
          content: extractionResult.extractedContent || queryResult.content,
          sources: queryResult.sources,
          metadata: {
            ...queryResult.metadata,
            extractionToolUsed: 'genericDocumentContentExtractorTool'
          }
        };
      } else {
        // For multiple documents, use the enhancedDocumentContentExtractorTool
        console.log(`QueryDocumentsAgent: Using enhancedDocumentContentExtractorTool for multiple document processing`);

        // Process with the enhanced tool with vector similarity enabled
        console.log(`QueryDocumentsAgent: Enabling vector similarity for multiple documents with query "${query}"`);

        // Get the namespaces from the metadata if available
        const namespaces = queryResult.metadata?.namespaces || [];
        console.log(`QueryDocumentsAgent: Using ${namespaces.length} namespaces for vector similarity:`, namespaces);

        const result = await enhancedDocumentContentExtractorTool.processDocuments({
          userId: userId,
          category,
          documentContent: queryResult.content, // Pass the content from queryDocumentsTool
          userQuery: query,
          performVectorSimilarity: true, // Enable vector similarity for better results
          consolidateResults: true,
          summarizeMultipleFiles: true,
          modelName: model || "deepseek-r1-distill-llama-70b",
          // Pass the namespace for vector similarity
          namespace: namespaces.length > 0 ? namespaces[0] : undefined
        });

        if (!result.success) {
          console.error(`QueryDocumentsAgent: Enhanced document extraction failed: ${result.error}`);
          return queryResult; // Return original result if extraction fails
        }

        // Return the consolidated content focused on the query
        return {
          success: true,
          content: result.extractedContent || result.consolidatedContent || queryResult.content,
          sources: queryResult.sources,
          metadata: {
            ...queryResult.metadata,
            extractionToolUsed: 'enhancedDocumentContentExtractorTool'
          }
        };
      }
    } catch (error) {
      console.error('Error processing documents for query:', error);
      return queryResult; // Return original result if processing fails
    }
  }

  async process(options: QueryDocumentsOptions): Promise<QueryDocumentsAgentResult> {
    try {
      console.log(`QueryDocumentsAgent: Processing query "${options.query}" for user ${options.userId}`);

      // First, analyze the query context to see if it has enough information
      // Pass filename and category as context to the analyzer
      // Note: For both filename-based and category-based requests, we'll automatically consider the query to have enough context
      console.log(`QueryDocumentsAgent: Processing query with options:`, {
        query: options.query,
        userId: options.userId,
        filename: options.filename,
        category: options.category,
        useInternetSearch: options.useInternetSearch
      });

      // Initialize contextAnalysis variable
      let contextAnalysis: {
        hasEnoughContext: boolean;
        missingContext: string[];
        enhancedQuery?: string;
        recommendation: string;
      } | undefined;

      // Skip context analysis if either filename, namespace, or category is provided
      if (options.namespace) {
        console.log(`QueryDocumentsAgent: Namespace provided (${options.namespace}), skipping context analysis`);
      } else if (options.filename) {
        console.log(`QueryDocumentsAgent: Filename provided (${options.filename}), skipping context analysis`);
      } else if (options.category) {
        console.log(`QueryDocumentsAgent: Category provided (${options.category}), skipping context analysis`);
      } else {
        contextAnalysis = await this.analyzeQueryContext(options.query);

        // If the query doesn't have enough context, return a helpful response
        if (!contextAnalysis.hasEnoughContext) {
          return {
            success: false,
            content: `I need more specific information to effectively search for documents. ${contextAnalysis.recommendation}\n\nMissing context includes:\n${contextAnalysis.missingContext.map(item => `- ${item}`).join('\n')}`,
            error: "Insufficient context for document search",
            metadata: {
              internetSearchUsed: false,
              functionCallingUsed: false
            }
          };
        }
      }

      // If we have an enhanced query from context analysis, use it
      let finalQuery = options.query;
      if (contextAnalysis?.enhancedQuery) {
        finalQuery = contextAnalysis.enhancedQuery;
      }

      // Set default model parameters if not provided
      const finalOptions = {
        ...options,
        query: finalQuery,
        model: options.model || "deepseek-r1-distill-llama-70b",
        modelOptions: options.modelOptions || {
          temperature: this.defaultTemperature,
          maxTokens: this.defaultMaxTokens
        }
      };

      // Log if namespace is provided
      if (options.namespace) {
        console.log(`QueryDocumentsAgent: Using provided namespace: ${options.namespace}`);
      }

      // For category-based queries, ensure we're handling multiple namespaces correctly
      if (options.category && options.category !== 'Unknown') {
        console.log(`QueryDocumentsAgent: Processing category-based query for category "${options.category}"`);

        // Get all namespaces for this category
        const namespaces = await this.getAllNamespacesForCategory(options.userId, options.category);
        console.log(`QueryDocumentsAgent: Found ${namespaces.length} namespaces for category "${options.category}":`, namespaces);

        if (namespaces.length === 0) {
          console.log(`QueryDocumentsAgent: No namespaces found for category "${options.category}"`);
          // Continue with normal processing, the queryDocumentsTool will handle the fallback
        }
      }

      // Step 1: Process with the base tool using the query
      const baseResult = await queryDocumentsTool.process(finalOptions);

      // If the base query failed and no documents were found
      if (!baseResult.success && baseResult.error?.includes("No namespaces found")) {
        console.log(`QueryDocumentsAgent: No documents found for query "${options.query}"`);

        // Generate a request for information using the RequestForInformationTool
        // Make sure contextAnalysis exists before accessing its properties
        const missingContext = contextAnalysis?.missingContext || [];

        const requestResult = await requestForInformationTool.process({
          query: options.query,
          missingContext: missingContext,
          category: options.category,
          attemptedSearches: [finalQuery],
          userId: options.userId
        });

        // If internet search is enabled, try that as well
        if (options.useInternetSearch) {
          console.log(`QueryDocumentsAgent: Trying internet search for "${options.query}"`);

          // Perform internet search
          const searchResult = await internetSearchTool.process({
            query: options.query,
            numResults: 5
          });

          if (searchResult.success) {
            // Generate follow-up questions based on the search result
            const followUpResult = await generateFollowUpQuestionsTool.process({
              query: options.query,
              responseContent: searchResult.content
            });

            // Find related categories
            const categoriesResult = await findRelatedCategoriesTool.process({
              userId: options.userId,
              currentCategory: options.category
            });

            // Return internet search result with enhanced features and request for information
            return {
              success: true,
              content: `[NOTE: The requested information was not found in your document collection. The following information has been sourced from the internet.]\n\n${searchResult.content}\n\n---\n\n${requestResult.requestMessage}\n\n${requestResult.specificQuestions.map(q => `- ${q}`).join('\n')}`,
              sources: searchResult.sources.map(source => ({
                title: source.title,
                doc_id: `web_${this._hashString(source.link)}`,
                page: 1, // Add page property to match Source type
                relevance: 0.7
              })),
              followUpQuestions: followUpResult.success ? followUpResult.questions : [],
              relatedCategories: categoriesResult.success ? categoriesResult.categories : [],
              metadata: {
                internetSearchUsed: true,
                requestForInformation: {
                  message: requestResult.requestMessage,
                  questions: requestResult.specificQuestions,
                  actions: requestResult.recommendedActions
                }
              }
            };
          }
        }

        // If internet search is not enabled or failed, return the request for information
        return {
          success: false,
          content: `${requestResult.requestMessage}\n\n**Specific Questions:**\n${requestResult.specificQuestions.map(q => `- ${q}`).join('\n')}\n\n**Recommended Actions:**\n${requestResult.recommendedActions.map(a => `- ${a}`).join('\n')}`,
          error: "No documents found for the specified query",
          metadata: {
            internetSearchUsed: false,
            functionCallingUsed: false,
            requestForInformation: {
              message: requestResult.requestMessage,
              questions: requestResult.specificQuestions,
              actions: requestResult.recommendedActions
            }
          }
        };
      }

      // If the base query failed for other reasons and internet search is enabled, try internet search
      if (!baseResult.success && options.useInternetSearch) {
        console.log(`QueryDocumentsAgent: Document query failed, trying internet search for "${options.query}"`);

        // Perform internet search
        const searchResult = await internetSearchTool.process({
          query: options.query,
          numResults: 5
        });

        if (searchResult.success) {
          // Generate follow-up questions based on the search result
          const followUpResult = await generateFollowUpQuestionsTool.process({
            query: options.query,
            responseContent: searchResult.content
          });

          // Find related categories
          const categoriesResult = await findRelatedCategoriesTool.process({
            userId: options.userId,
            currentCategory: options.category
          });

          // Return internet search result with enhanced features
          return {
            success: true,
            content: `[NOTE: The requested information was not found in your document collection. The following information has been sourced from the internet.]\n\n${searchResult.content}`,
            sources: searchResult.sources.map(source => ({
              title: source.title,
              doc_id: `web_${this._hashString(source.link)}`,
              page: 1, // Add page property to match Source type
              relevance: 0.7
            })),
            followUpQuestions: followUpResult.success ? followUpResult.questions : [],
            relatedCategories: categoriesResult.success ? categoriesResult.categories : [],
            metadata: {
              internetSearchUsed: true
            }
          };
        }

        // If internet search also failed, return the original error
        return {
          ...baseResult
        };
      }

      // If the base query failed and internet search is not enabled, return the error
      if (!baseResult.success) {
        return {
          ...baseResult
        };
      }

      // Step 2: Process the documents to extract information relevant to the query
      console.log(`QueryDocumentsAgent: Base query successful, processing documents for relevant information`);
      const processedResult = await this.processDocumentsForQuery(
        options.query,
        baseResult,
        options.userId,
        options.category,
        options.filename,
        options.model,
        options.modelOptions
      );

      // Step 3: Generate follow-up questions based on the processed result
      const followUpResult = await generateFollowUpQuestionsTool.process({
        query: options.query,
        responseContent: processedResult.content
      });

      // Step 4: Find related categories that might contain relevant information
      const categoriesResult = await findRelatedCategoriesTool.process({
        userId: options.userId,
        currentCategory: options.category
      });

      // Process the response with the markdown-renderer-tool
      let formattedContent = processedResult.content;
      try {
        const markdownResult = await markdownRendererTool.process({
          markdown: processedResult.content,
          operation: 'preprocess'
        });

        if (markdownResult.success) {
          formattedContent = markdownResult.content;
        } else {
          console.warn("Failed to process response with markdown-renderer-tool:", markdownResult.error);
        }
      } catch (error) {
        console.error("Error processing response with markdown-renderer-tool:", error);
      }

      // Step 5: Return enhanced result with formatted content
      return {
        ...processedResult,
        content: formattedContent,
        followUpQuestions: followUpResult.success ? followUpResult.questions : [],
        relatedCategories: categoriesResult.success ? categoriesResult.categories : []
      };
    } catch (error) {
      console.error("Error in QueryDocumentsAgent:", error);
      return {
        success: false,
        content: "",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Generate a hash string from a URL for use as a document ID
   * @param str - String to hash
   * @returns Hash string
   */
  private _hashString(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Get a list of all unique categories for a user
   *
   * @param userId - The user ID
   * @returns An array of category names
   */
  async getAllCategories(userId: string): Promise<string[]> {
    return findRelatedCategoriesTool.getAllCategories(userId);
  }

  /**
   * Get all namespaces associated with a specific category
   *
   * @param userId - The user ID
   * @param category - The category to get namespaces for
   * @returns An array of namespace strings
   */
  async getAllNamespacesForCategory(userId: string, category: string): Promise<string[]> {
    try {
      console.log(`QueryDocumentsAgent: Getting all namespaces for category "${category}"`);

      // Query Firestore to get all files with the specified category
      const snapshot = await adminDb.collection('users')
        .doc(userId)
        .collection('files')
        .where('category', '==', category)
        .get();

      // Extract namespaces from the query results
      const namespaces = snapshot.docs
        .map(doc => {
          const data = doc.data();
          console.log(`QueryDocumentsAgent: File "${data.name || doc.id}" has namespace "${data.namespace || 'undefined'}"`);
          return data.namespace;
        })
        .filter((namespace): namespace is string => !!namespace);

      console.log(`QueryDocumentsAgent: Found ${namespaces.length} namespaces for category "${category}":`, namespaces);

      // If no namespaces found, log a warning
      if (namespaces.length === 0) {
        console.warn(`QueryDocumentsAgent: No namespaces found for category "${category}". This may cause issues with document retrieval.`);
      }

      return namespaces;
    } catch (error) {
      console.error(`QueryDocumentsAgent: Error getting namespaces for category "${category}":`, error);
      return [];
    }
  }

  /**
   * Process a query with custom input text instead of documents
   *
   * @param query - The user's query
   * @param customContent - The custom content to search
   * @param userId - Optional user ID (can be null for custom content)
   * @param model - Optional model to use for processing
   * @param modelOptions - Optional model configuration options
   * @returns Query result with content and metadata
   */
  async processCustomContent(
    query: string,
    customContent: string,
    userId?: string,
    model?: string,
    modelOptions?: Record<string, any>
  ): Promise<QueryDocumentsAgentResult> {
    try {
      console.log(`QueryDocumentsAgent: Processing query "${query}" with custom content`);

      // Create a focused extraction prompt that only extracts information relevant to the query
      console.log(`QueryDocumentsAgent: Enabling vector similarity for query "${query}" on custom content`);

      const extractionResult = await genericDocumentContentExtractorTool.process({
        documentContent: customContent,
        documentTitle: 'Custom Content',
        userQuery: query,
        performVectorSimilarity: true, // Enable vector similarity for better results
        userId: userId, // This can be undefined for custom content
        modelName: model || "deepseek-r1-distill-llama-70b",
        modelOptions: modelOptions // Pass model options if provided
      });

      if (!extractionResult.success) {
        console.error(`QueryDocumentsAgent: Document extraction failed: ${extractionResult.error}`);
        return {
          success: false,
          content: "",
          error: extractionResult.error || "Failed to extract information from custom content"
        };
      }

      // Generate follow-up questions based on the processed result
      const followUpResult = await generateFollowUpQuestionsTool.process({
        query: query,
        responseContent: extractionResult.extractedContent || customContent
      });

      // Process the response with the markdown-renderer-tool
      let formattedContent = extractionResult.extractedContent || customContent;
      try {
        const markdownResult = await markdownRendererTool.process({
          markdown: formattedContent,
          operation: 'preprocess'
        });

        if (markdownResult.success) {
          formattedContent = markdownResult.content;
        } else {
          console.warn("Failed to process response with markdown-renderer-tool:", markdownResult.error);
        }
      } catch (error) {
        console.error("Error processing response with markdown-renderer-tool:", error);
      }

      // Return the extracted content focused on the query
      return {
        success: true,
        content: formattedContent,
        followUpQuestions: followUpResult.success ? followUpResult.questions : [],
        metadata: {
          extractionToolUsed: 'genericDocumentContentExtractorTool',
          internetSearchUsed: false,
          functionCallingUsed: false
        }
      };
    } catch (error) {
      console.error("Error processing custom content:", error);
      return {
        success: false,
        content: "",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Process a multi-category query by searching across multiple categories
   * and combining the results
   *
   * @param options - Base query options
   * @param categories - Array of categories to search
   * @returns Combined query results
   */
  async processMultiCategory(
    options: Omit<QueryDocumentsOptions, 'category' | 'filename'>,
    categories: string[]
  ): Promise<QueryDocumentsAgentResult> {
    try {
      if (categories.length === 0) {
        return {
          success: false,
          content: "",
          error: "No categories provided for multi-category search"
        };
      }

      if (categories.length === 1) {
        // If only one category, use the standard process method
        return this.process({
          ...options,
          category: categories[0]
        });
      }

      console.log(`QueryDocumentsAgent: Processing multi-category query across ${categories.length} categories`);

      // Set default model parameters if not provided
      const finalOptions = {
        ...options,
        model: options.model || "deepseek-r1-distill-llama-70b",
        modelOptions: options.modelOptions || {
          temperature: this.defaultTemperature,
          maxTokens: this.defaultMaxTokens
        }
      };

      // Get all namespaces for each category
      const categoryNamespacesMap = new Map<string, string[]>();
      for (const category of categories) {
        const namespaces = await this.getAllNamespacesForCategory(options.userId, category);
        console.log(`QueryDocumentsAgent: Found ${namespaces.length} namespaces for category "${category}":`, namespaces);
        categoryNamespacesMap.set(category, namespaces);
      }

      // Log the total number of namespaces across all categories
      const totalNamespaces = Array.from(categoryNamespacesMap.values()).reduce((sum, namespaces) => sum + namespaces.length, 0);
      console.log(`QueryDocumentsAgent: Processing multi-category query with ${totalNamespaces} total namespaces across ${categories.length} categories`);

      // Process each category in parallel
      const categoryResults = await Promise.all(
        categories.map(category =>
          queryDocumentsTool.process({
            ...finalOptions,
            category
          })
        )
      );

      // Filter successful results
      const successfulResults = categoryResults.filter(result => result.success);

      if (successfulResults.length === 0) {
        return {
          success: false,
          content: "",
          error: "No relevant content found across any of the specified categories"
        };
      }

      // Combine content from all successful results
      const combinedContent = successfulResults
        .map(result => `Category: ${result.metadata?.category || 'Unknown'}\n\n${result.content}`)
        .join('\n\n---\n\n');

      // Combine sources
      const combinedSources = successfulResults
        .flatMap(result => result.sources || [])
        .sort((a, b) => (b.relevance || 0) - (a.relevance || 0))
        .slice(0, this.maxResults);

      // Create a combined result object to pass to processDocumentsForQuery
      const combinedResult: QueryDocumentsAgentResult = {
        success: true,
        content: combinedContent,
        sources: combinedSources,
        metadata: {
          chunkCount: successfulResults.reduce((sum, result) => sum + (result.metadata?.chunkCount || 0), 0),
          averageRelevance: successfulResults.reduce((sum, result) => sum + (result.metadata?.averageRelevance || 0), 0) / successfulResults.length,
          internetSearchUsed: successfulResults.some(result => result.metadata?.internetSearchUsed),
          namespaces: successfulResults.flatMap(result => result.metadata?.namespaces || []),
          category: categories.join(', ')
        }
      };

      // Process the combined content with the appropriate document extractor tool
      console.log(`QueryDocumentsAgent: Processing multi-category results with processDocumentsForQuery`);
      const processedResult = await this.processDocumentsForQuery(
        options.query,
        combinedResult,
        options.userId,
        categories.join(', '), // Pass all categories as a comma-separated string
        undefined, // No specific filename for multi-category
        options.model,
        options.modelOptions
      );

      // Generate follow-up questions based on the processed result
      const followUpResult = await generateFollowUpQuestionsTool.process({
        query: options.query,
        responseContent: processedResult.content
      });

      // Process the response with the markdown-renderer-tool
      let formattedContent = processedResult.content;
      try {
        const markdownResult = await markdownRendererTool.process({
          markdown: processedResult.content,
          operation: 'preprocess'
        });

        if (markdownResult.success) {
          formattedContent = markdownResult.content;
        } else {
          console.warn("Failed to process response with markdown-renderer-tool:", markdownResult.error);
        }
      } catch (error) {
        console.error("Error processing response with markdown-renderer-tool:", error);
      }

      return {
        success: true,
        content: formattedContent,
        sources: combinedSources,
        followUpQuestions: followUpResult.success ? followUpResult.questions : [],
        metadata: {
          ...processedResult.metadata,
          category: categories.join(', ')
        }
      };
    } catch (error) {
      console.error("Error in multi-category processing:", error);
      return {
        success: false,
        content: "",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      };
    }
  }

  /**
   * Synthesize a unified response from multi-category search results
   *
   * @param query - The user's query
   * @param combinedContent - Combined content from all categories
   * @param categories - The categories that were searched
   * @returns A synthesized response
   */
  // This method is no longer needed as we're using processDocumentsForQuery instead
  // Keeping it as a comment for reference
  /*
  private async synthesizeMultiCategoryResponse(
    query: string,
    combinedContent: string,
    categories: string[]
  ): Promise<string> {
    try {
      const systemPrompt = `
You are an AI assistant that synthesizes information from multiple document categories.
Your task is to create a unified, coherent response based on information retrieved from different categories.

User Query: "${query}"

Information from multiple categories:
${combinedContent}

Categories searched: ${categories.join(', ')}

Create a comprehensive response that:
1. Addresses the user's query directly
2. Integrates information from all relevant categories
3. Highlights any differences or complementary information between categories
4. Provides a clear, well-structured answer

FORMATTING REQUIREMENTS:
1. Format your response using proper markdown syntax
2. Use headings (## for main sections, ### for subsections) to organize information
3. Use bullet points (- ) or numbered lists (1. ) for listing items
4. Use **bold** for emphasis on important points
5. DO NOT include any technical information like namespace IDs or internal identifiers
6. Make sure your response is grammatically correct and well-structured
7. Use clean, simple markdown formatting without complex nested structures

Your response should be informative, concise, and well-organized.
`;

      return await processWithGroq({
        prompt: systemPrompt,
        model: "deepseek-r1-distill-llama-70b",
        modelOptions: {
          temperature: this.defaultTemperature,
          maxTokens: this.defaultMaxTokens
        }
      });
    } catch (error) {
      console.error("Error synthesizing multi-category response:", error);

      // Create a fallback response
      const fallbackResponse = `I found information across multiple categories (${categories.join(', ')}), but encountered an error synthesizing a unified response. Here's the raw information:\n\n${combinedContent}`;

      // Process the fallback response with the markdown-renderer-tool
      try {
        const markdownResult = await markdownRendererTool.process({
          markdown: fallbackResponse,
          operation: 'preprocess'
        });

        if (markdownResult.success) {
          return markdownResult.content;
        }
      } catch (processingError) {
        console.error("Error processing fallback response with markdown-renderer-tool:", processingError);
      }

      return fallbackResponse;
    }
  }
  */

  /**
   * Process a query using function calling with the Groq model
   *
   * @param query - The user's query
   * @param userId - The user ID
   * @param context - Optional context information
   * @returns The processed result
   */
  async processWithFunctionCalling(
    query: string,
    userId: string,
    context?: {
      category?: string;
      filename?: string;
      namespace?: string;
      useInternetSearch?: boolean;
      model?: string;
      modelOptions?: Record<string, any>;
    }
  ): Promise<QueryDocumentsAgentResult> {
    try {
      console.log(`QueryDocumentsAgent: Processing query with function calling: "${query}"`);

      // If a filename is provided, we can skip context analysis
      // This is important because queries like "summarize the content of the page"
      // don't have enough context on their own, but are valid when a filename is provided
      console.log(`QueryDocumentsAgent: Processing with function calling:`, {
        query,
        userId,
        filename: context?.filename,
        namespace: context?.namespace,
        category: context?.category,
        useInternetSearch: context?.useInternetSearch
      });

      // Skip context analysis if either filename, namespace, or category is provided
      if (context?.namespace) {
        console.log(`QueryDocumentsAgent: Namespace provided (${context.namespace}), skipping context analysis`);
        // When using a namespace, don't set a category
        // Files selected from the file list should be treated as individual files with category 'Unknown'
        delete context.category; // Remove any category that might have been set
      } else if (context?.filename) {
        console.log(`QueryDocumentsAgent: Filename provided (${context.filename}), skipping context analysis`);
      } else if (context?.category) {
        console.log(`QueryDocumentsAgent: Category provided (${context.category}), skipping context analysis`);

        // For category-based queries, ensure we're handling multiple namespaces correctly
        if (context.category !== 'Unknown') {
          console.log(`QueryDocumentsAgent: Processing category-based query for category "${context.category}" with function calling`);

          // Get all namespaces for this category
          const namespaces = await this.getAllNamespacesForCategory(userId, context.category);
          console.log(`QueryDocumentsAgent: Found ${namespaces.length} namespaces for category "${context.category}":`, namespaces);

          if (namespaces.length === 0) {
            console.log(`QueryDocumentsAgent: No namespaces found for category "${context.category}" with function calling`);
            // Continue with normal processing, the queryDocumentsTool will handle the fallback
          }
        }
      } else {
        // Only perform context analysis if neither filename nor category is provided
        const contextAnalysis = await this.analyzeQueryContext(query);

        if (!contextAnalysis.hasEnoughContext) {
          // Format the missing context as a list if it exists
          const missingContextList = contextAnalysis.missingContext && contextAnalysis.missingContext.length > 0
            ? `\n\nMissing context includes:\n${contextAnalysis.missingContext.map(item => `- ${item}`).join('\n')}`
            : '';

          return {
            success: false,
            content: `I need more specific information to effectively search for documents. ${contextAnalysis.recommendation}${missingContextList}`,
            error: "Insufficient context for document search",
            metadata: {
              internetSearchUsed: false,
              functionCallingUsed: true
            }
          };
        }
      }

      // Prepare the request to the Groq API
      const response = await fetch('https://api.groq.com/openai/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`
        },
        body: JSON.stringify({
          model: "deepseek-r1-distill-llama-70b",
          messages: [
            {
              role: "system",
              content: `You are an AI assistant that helps users find information in their documents.
You have access to several tools that can help you answer the user's questions:
- queryDocumentsTool: Search through the user's documents based on a query.
- findInternalDocumentsTool: Search for documents by name or category.
- internetSearchTool: Search the internet if document search doesn't yield results or if requested.
- generateFollowUpQuestionsTool: Generate follow-up questions based on search results or provided content.
- findRelatedCategoriesTool: Find related document categories for the current user.
- markdownRendererTool: Format content in markdown (typically used by other tools, but available).
- genericDocumentContentExtractorTool: Analyzes and extracts detailed information (like type, purpose, key points, takeaways) from a given piece of document text. Useful for in-depth understanding of specific content.
- enhancedDocumentContentExtractorTool: Retrieves and analyzes content from specific documents (by name, ID/namespace, or category) or provided text. Can consolidate findings from multiple files and optionally summarize them, focusing on a user's query if provided.

Use these tools as needed to provide the most helpful response. Ensure all required arguments for tools are provided. The 'userId' is particularly important for tools accessing user-specific data.

FORMATTING REQUIREMENTS:
1. Format your response using proper markdown syntax
2. Use headings (## for main sections, ### for subsections) to organize information
3. Use bullet points (- ) or numbered lists (1. ) for listing items
4. Use **bold** for emphasis on important points
5. DO NOT include any technical information like namespace IDs or internal identifiers
6. Make sure your response is grammatically correct and well-structured
7. Use clean, simple markdown formatting without complex nested structures`
            },
            {
              role: "user",
              content: query
            }
          ],
          tools: this.getToolDefinitions(),
          tool_choice: "auto",
          temperature: this.defaultTemperature,
          max_tokens: this.defaultMaxTokens
        })
      });

      if (!response.ok) {
        throw new Error(`Groq API error: ${response.statusText}`);
      }

      const data = await response.json();

      // Check if the model wants to call a tool
      if (data.choices &&
          data.choices[0] &&
          data.choices[0].message &&
          data.choices[0].message.tool_calls &&
          data.choices[0].message.tool_calls.length > 0) {

        // Process tool calls
        const toolCalls = data.choices[0].message.tool_calls;
        const toolResults = await Promise.all(toolCalls.map(async (toolCall: any) => {
          const { name, arguments: args } = toolCall.function;
          const parsedArgs = JSON.parse(args);

          console.log(`Processing tool call: ${name} with args:`, parsedArgs);

          // Execute the appropriate tool based on the name
          switch (name) {
            case 'queryDocumentsTool':
              return await queryDocumentsTool.process({
                ...parsedArgs,
                userId, // Ensure agent's userId is passed
                category: parsedArgs.category || context?.category,
                filename: parsedArgs.filename || context?.filename,
                namespace: parsedArgs.namespace || context?.namespace,
                useInternetSearch: parsedArgs.useInternetSearch || context?.useInternetSearch,
                model: parsedArgs.model || context?.model || "deepseek-r1-distill-llama-70b",
                modelOptions: parsedArgs.modelOptions || context?.modelOptions || {
                  temperature: this.defaultTemperature,
                  maxTokens: this.defaultMaxTokens
                }
              });

            case 'generateFollowUpQuestions':
              return await generateFollowUpQuestionsTool.process(parsedArgs);

            case 'findRelatedCategories':
              return await findRelatedCategoriesTool.process({
                ...parsedArgs,
                userId // Ensure agent's userId is passed
              });

            case 'findInternalDocuments':
              return await findInternalDocumentsTool.process({
                ...parsedArgs,
                userId // Ensure agent's userId is passed
              });

            case 'internetSearch':
              return await internetSearchTool.process(parsedArgs);

            case 'markdownRenderer':
              return await markdownRendererTool.process(parsedArgs);

            // Added cases for new tools
            case 'genericDocumentContentExtractorTool':
              return await genericDocumentContentExtractorTool.process({
                ...parsedArgs,
                // userId is optional for this tool, but pass it if available from LLM or agent context
                userId: parsedArgs.userId || userId
              });

            case 'enhancedDocumentContentExtractorTool':
              // userId is required by this tool's schema. LLM should provide it.
              // We ensure it's passed, prioritizing LLM's arg then agent's context.
              return await enhancedDocumentContentExtractorTool.processDocuments({
                ...parsedArgs,
                userId: parsedArgs.userId || userId
              });

            default:
              return {
                success: false,
                content: '',
                error: `Unknown tool: ${name}`
              };
          }
        }));

        // Send the tool results back to the model
        const secondResponse = await fetch('https://api.groq.com/openai/v1/chat/completions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.GROQ_API_KEY}`
          },
          body: JSON.stringify({
            model: "deepseek-r1-distill-llama-70b",
            messages: [
              {
                role: "system",
                content: `You are an AI assistant that helps users find information in their documents.
You have access to several tools that can help you answer the user's questions.
Provide a helpful, informative response based on the tool results.

FORMATTING REQUIREMENTS:
1. Format your response using proper markdown syntax
2. Use headings (## for main sections, ### for subsections) to organize information
3. Use bullet points (- ) or numbered lists (1. ) for listing items
4. Use **bold** for emphasis on important points
5. DO NOT include any technical information like namespace IDs or internal identifiers
6. Make sure your response is grammatically correct and well-structured
7. Use clean, simple markdown formatting without complex nested structures`
              },
              {
                role: "user",
                content: query
              },
              data.choices[0].message, // Original assistant message with tool_calls
              // Send all tool results back
              ...toolResults.map((result, index) => ({
                role: "tool",
                tool_call_id: toolCalls[index].id, // Match tool_call_id for each result
                content: JSON.stringify(result) // Send stringified JSON of the tool's output
              }))
            ],
            temperature: this.defaultTemperature,
            max_tokens: this.defaultMaxTokens
          })
        });

        if (!secondResponse.ok) {
          throw new Error(`Groq API error: ${secondResponse.statusText}`);
        }

        const secondData = await secondResponse.json();

        // Extract the final response
        const finalResponse = secondData.choices[0].message.content;

        // Extract follow-up questions and related categories from tool results
        const followUpQuestions = toolResults.find((result: any) =>
          result && result.questions && Array.isArray(result.questions)
        )?.questions || [];

        const relatedCategories = toolResults.find((result: any) =>
          result && result.categories && Array.isArray(result.categories)
        )?.categories || [];

        // Extract matching documents from findInternalDocumentsTool results
        const matchingDocuments = toolResults.find((result: any) =>
          result && result.documents && Array.isArray(result.documents)
        )?.documents || [];

        // Extract sources from document query results (queryDocumentsTool or internetSearchTool)
        let sources: QueryDocumentsResult['sources'] = [];
        const queryDocToolResult = toolResults.find((result: any) =>
            result && result.sources && Array.isArray(result.sources) && result.metadata?.chunkCount !== undefined // Heuristic for queryDocumentsTool
        );

        // Check if we have a document query result that we can process further
        if (queryDocToolResult) {
            sources = queryDocToolResult.sources;

            // Create a result object to pass to processDocumentsForQuery
            const baseResult: QueryDocumentsAgentResult = {
                success: true,
                content: queryDocToolResult.content,
                sources: queryDocToolResult.sources,
                metadata: queryDocToolResult.metadata
            };

            // Process the document content with the appropriate extractor tool
            console.log(`QueryDocumentsAgent: Processing function calling result with processDocumentsForQuery`);
            const processedResult = await this.processDocumentsForQuery(
                query,
                baseResult,
                userId,
                context?.category,
                context?.filename,
                context?.model
            );

            // Process the response with the markdown-renderer-tool
            let formattedContent = processedResult.content;
            try {
                const markdownResult = await markdownRendererTool.process({
                    markdown: processedResult.content,
                    operation: 'preprocess'
                });

                if (markdownResult.success) {
                    formattedContent = markdownResult.content;
                } else {
                    console.warn("Failed to process response with markdown-renderer-tool:", markdownResult.error);
                }
            } catch (error) {
                console.error("Error processing response with markdown-renderer-tool:", error);
            }

            return {
                success: true,
                content: formattedContent,
                followUpQuestions,
                relatedCategories,
                matchingDocuments,
                sources,
                metadata: {
                    ...processedResult.metadata,
                    functionCallingUsed: true
                }
            };
        } else {
            // If we don't have a document query result, check for internet search result
            const internetSearchResult = toolResults.find((result: any) =>
                result && result.sources && Array.isArray(result.sources) && result.metadata?.internetSearchUsed === true // Heuristic for internetSearchTool
            );

            if (internetSearchResult) {
                sources = internetSearchResult.sources.map((source: any) => ({ // Adapt internet search sources
                    title: source.title,
                    doc_id: `web_${this._hashString(source.link)}`,
                    page: 1,
                    relevance: 0.7 // Example relevance
                }));
            }

            // Process the final response with the markdown-renderer-tool
            let formattedResponse = finalResponse;
            try {
                const markdownResult = await markdownRendererTool.process({
                    markdown: finalResponse,
                    operation: 'preprocess'
                });

                if (markdownResult.success) {
                    formattedResponse = markdownResult.content;
                } else {
                    console.warn("Failed to process final response with markdown-renderer-tool:", markdownResult.error);
                }
            } catch (error) {
                console.error("Error processing final response with markdown-renderer-tool:", error);
            }

            return {
                success: true,
                content: formattedResponse,
                followUpQuestions,
                relatedCategories,
                matchingDocuments,
                sources,
                metadata: {
                    functionCallingUsed: true,
                    internetSearchUsed: internetSearchResult ? true : false
                }
            };
        }
      } else {
        // If no tool calls, just return the model's response
        const content = data.choices[0].message.content;

        // Process the response with the markdown-renderer-tool
        let formattedResponse = content;
        try {
          const markdownResult = await markdownRendererTool.process({
            markdown: content,
            operation: 'preprocess'
          });

          if (markdownResult.success) {
            formattedResponse = markdownResult.content;
          } else {
            console.warn("Failed to process response with markdown-renderer-tool:", markdownResult.error);
          }
        } catch (error) {
          console.error("Error processing response with markdown-renderer-tool:", error);
        }

        return {
          success: true,
          content: formattedResponse,
          metadata: {
            functionCallingUsed: false
          }
        };
      }
    } catch (error) {
      console.error("Error in function calling:", error);

      // Fall back to regular processing
      console.log("Falling back to regular processing");
      return this.process({
        query,
        userId,
        category: context?.category,
        filename: context?.filename,
        namespace: context?.namespace,
        useInternetSearch: context?.useInternetSearch,
        model: context?.model,
        modelOptions: context?.modelOptions
      });
    }
  }
  /**
   * Query documents by file IDs
   *
   * @param params - Query parameters
   * @returns Query results
   */
  async queryDocuments(params: {
    query: string;
    fileIds: string[];
    maxResults: number;
    userId?: string;
  }): Promise<{ success: boolean; results: string[]; chunks?: any[]; error?: string }> {
    console.log(`QueryDocumentsAgent: Querying documents by fileIds`, params);

    try {
      if (!params.fileIds || params.fileIds.length === 0) {
        return {
          success: false,
          results: [],
          error: "No file IDs provided"
        };
      }

      // Get the first fileId
      const fileId = params.fileIds[0];

      // Check if this is a UUID format (likely a namespace) or a filename
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(fileId);

      console.log(`QueryDocumentsAgent: FileId ${fileId} appears to be a ${isUUID ? 'namespace' : 'filename'}`);

      // Create options for process method
      const processOptions: any = {
        query: params.query,
        userId: params.userId || '',
        maxResults: params.maxResults || 5
      };

      // If it looks like a UUID/namespace, pass it as namespace instead of filename
      if (isUUID) {
        processOptions.namespace = fileId;
        // When using a namespace, don't set a default category
        // Files selected from the file list should be treated as individual files with category 'Unknown'
      } else {
        // Otherwise treat it as a filename
        processOptions.filename = fileId;
        // Only add a default category for non-namespace files as a fallback
        processOptions.category = 'Unknown';
      }

      const result = await this.process(processOptions);

      return {
        success: result.success,
        results: result.success ? [result.content] : [],
        chunks: result.chunks,
        error: result.error
      };
    } catch (error) {
      console.error("Error in queryDocuments:", error);
      return {
        success: false,
        results: [],
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Query documents by category IDs
   *
   * @param params - Query parameters
   * @returns Query results
   */
  async queryDocumentsByCategory(params: {
    query: string;
    categoryIds: string[];
    maxResults: number;
    userId?: string;
  }): Promise<{ success: boolean; results: string[]; chunks?: any[]; error?: string }> {
    console.log(`QueryDocumentsAgent: Querying documents by categoryIds`, params);

    try {
      if (params.categoryIds.length === 0) {
        return {
          success: false,
          results: [],
          error: "No category IDs provided"
        };
      }

      // Process each category in parallel
      const categoryResults = await Promise.all(
        params.categoryIds.map(category =>
          this.process({
            query: params.query,
            userId: params.userId || '',
            category, // This satisfies the requirement for a category
            maxResults: params.maxResults || 5
          })
        )
      );

      // Filter successful results
      const successfulResults = categoryResults.filter(result => result.success);

      if (successfulResults.length === 0) {
        return {
          success: false,
          results: [],
          error: "No relevant content found across any of the specified categories"
        };
      }

      // Combine results
      return {
        success: true,
        results: successfulResults.map(result => result.content),
        chunks: successfulResults.flatMap(result => result.chunks || [])
      };
    } catch (error) {
      console.error("Error in queryDocumentsByCategory:", error);
      return {
        success: false,
        results: [],
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }
}

// Export a singleton instance for easy import
export const queryDocumentsAgent = new QueryDocumentsAgent();